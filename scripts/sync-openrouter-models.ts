// scripts/sync-openrouter-models.ts

import type { Database, Json, TablesInsert } from '@/types/database.types';
import { createClient, SupabaseClient } from '@supabase/supabase-js';


const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_ROLE_KEY = process.env.SUPABASE_ROLE_KEY;

type OpenRouterModel = {
  id: string;
  name?: string;
  provider?: string; // Not always present; derive from id when missing
  description?: string;
  context_length?: number;
  capabilities?: Record<string, unknown>;
  architecture?: Record<string, unknown>;
  pricing?: Record<string, unknown>;
  parameters?: unknown[];
  canonical_slug?: string; // Optional in OpenRouter responses
  [key: string]: unknown;
};
type LLMModelInsert = TablesInsert<'llm_models'>;

// Local tier enum aligned with DB enum (no enterprise in app plans)
const MODEL_TIERS = ['free', 'starter', 'premium'] as const;
type ModelTier = (typeof MODEL_TIERS)[number];

function parseNumberField(value: unknown): number {
  if (typeof value === 'number' && Number.isFinite(value)) return value;
  if (typeof value === 'string') {
    const n = parseFloat(value);
    return Number.isFinite(n) ? n : 0;
  }
  return 0;
}

function computeTier(model: OpenRouterModel): ModelTier {
  const prompt = parseNumberField((model.pricing as Record<string, unknown> | undefined)?.prompt);
  const completion = parseNumberField((model.pricing as Record<string, unknown> | undefined)?.completion);
  const blendedPer1k = (prompt + 2 * completion) * 1000;

  let idx = blendedPer1k <= 0.001 ? 0 : blendedPer1k <= 0.01 ? 1 : 2;

  const ctx = typeof model.context_length === 'number' ? model.context_length : 0;
  if (ctx >= 128_000) idx = Math.min(idx + 1, MODEL_TIERS.length - 1);

  const inputModalitiesRaw = Array.isArray((model.architecture as Record<string, unknown> | undefined)?.input_modalities)
    ? (((model.architecture as Record<string, unknown>).input_modalities as unknown[]) || [])
    : [];
  const modalities = inputModalitiesRaw.filter((v): v is string => typeof v === 'string');
  if (modalities.includes('image')) idx = Math.min(idx + 1, MODEL_TIERS.length - 1);

  return MODEL_TIERS[idx];
}


type CliOptions = {
  providers: string[]; // lowercased
  all: boolean;
  group: boolean;
  format: 'json' | 'ndjson';
  includeInactive: boolean;
  timeoutMs: number;
  update: boolean; // when true, upsert into supabase
};

function parseCliArgs(argv: string[]): CliOptions {
  const providers: string[] = [];
  let group = false;
  let all = false;
  let format: 'json' | 'ndjson' = 'json';
  let includeInactive = false;
  let timeoutMs = 15_000;
  let update = false;

  for (let i = 2; i < argv.length; i++) {
    const arg = argv[i];
    if (arg === '--all') all = true;
    else if (arg === '--group') group = true;
    else if (arg.startsWith('--format=')) {
      const val = arg.split('=')[1];
      if (val === 'json' || val === 'ndjson') format = val;
    } else if (arg.startsWith('--providers=')) {
      const list = arg.split('=')[1] || '';
      list
        .split(',')
        .map((s) => s.trim())
        .filter(Boolean)
        .forEach((p) => providers.push(p.toLowerCase()));
    } else if (arg === '--include-inactive') {
      includeInactive = true;
    } else if (arg === '--update' || arg === '--sync') {
      update = true;
    } else if (arg.startsWith('--timeout=')) {
      const val = Number(arg.split('=')[1]);
      if (!Number.isNaN(val) && val > 0) timeoutMs = val;
    } else if (!arg.startsWith('--')) {
      providers.push(arg.toLowerCase());
    }
  }

  return { providers, all, group, format, includeInactive, timeoutMs, update };
}

function getProviderFromModel(model: OpenRouterModel): string | null {
  if (model.provider && typeof model.provider === 'string') {
    return model.provider.toLowerCase();
  }
  // Fallback: derive from id like "openai/gpt-4o" → "openai"
  if (model.id && model.id.includes('/')) {
    return model.id.split('/')[0]?.toLowerCase() || null;
  }
  return null;
}

function getProviderFromIdentifier(identifier?: string | null): string | null {
  if (!identifier) return null;
  if (identifier.includes('/')) {
    return identifier.split('/')[0]?.toLowerCase() || null;
  }
  return null;
}

function buildHeaders(): Record<string, string> {
  const headers: Record<string, string> = {};
  if (process.env.OPENROUTER_API_KEY) {
    headers.Authorization = `Bearer ${process.env.OPENROUTER_API_KEY}`;
  }
  if (process.env.OPENROUTER_SITE_URL) {
    headers['HTTP-Referer'] = process.env.OPENROUTER_SITE_URL;
  }
  headers['X-Title'] = process.env.OPENROUTER_APP_NAME || 'Chatty App';
  return headers;
}

function createSupabaseIfAvailable(): SupabaseClient<Database> | null {

  const url = SUPABASE_URL;
  const key = SUPABASE_ROLE_KEY;
  if (!url || !key) return null;
  return createClient<Database>(url, key, {
    auth: { persistSession: false },
  });
}

async function fetchProviders(
  client: SupabaseClient<Database>,
  providerNames: string[],
  all: boolean
) {
  if (all) {
    const { data, error } = await client
      .from('llm_providers')
      .select('id, name, openrouter_name, is_active, supports_openrouter');
    if (error) throw error;
    return data ?? [];
  }
  // match on either internal name or openrouter_name
  const { data, error } = await client
    .from('llm_providers')
    .select('id, name, openrouter_name, is_active, supports_openrouter')
    .or(
      providerNames
        .map((n) => `name.eq.${n},openrouter_name.eq.${n}`)
        .join(',')
    );
  if (error) throw error;
  return data ?? [];
}

async function getFetch(): Promise<typeof globalThis.fetch> {
  if (typeof globalThis.fetch === 'function') return globalThis.fetch.bind(globalThis);
  // Fallback to node-fetch ESM import when not available
  const mod = (await import('node-fetch')) as unknown as { default: typeof globalThis.fetch };
  return mod.default;
}

async function fetchWithTimeout(
  url: string,
  options: RequestInit & { timeoutMs?: number } = {}
) {
  const controller = new AbortController();
  const id = setTimeout(() => controller.abort(), options.timeoutMs ?? 15000);
  try {
    const f = await getFetch();
    return await f(url, { ...options, signal: controller.signal });
  } finally {
    clearTimeout(id);
  }
}

function mapModelToLLMModel(
  model: OpenRouterModel,
  providerId: string | null = null
): LLMModelInsert {
  const capabilitiesUnknown = (model.capabilities || {}) as Record<string, unknown>;
  const architectureUnknown = (model.architecture || {}) as Record<string, unknown>;
  // Heuristics for certain booleans
  const allowsToolUsage = Boolean(
    capabilitiesUnknown['tools'] ||
      capabilitiesUnknown['function_calling'] ||
      capabilitiesUnknown['tool_use']
  );
  const inputModalitiesRaw = Array.isArray(architectureUnknown['input_modalities'])
    ? (architectureUnknown['input_modalities'] as unknown[])
    : [];
  const inputModalities = inputModalitiesRaw.filter(
    (v): v is string => typeof v === 'string'
  );
  const allowsFileUpload = Boolean(
    capabilitiesUnknown['file_upload'] ||
      inputModalities.includes('image') ||
      inputModalities.includes('audio')
  );

  const rawName = (model.name ?? model.id) as string;
  const splitName = rawName.split(':').pop() ?? rawName;

  // Safely derive a source for the OpenRouter name. Prefer canonical_slug when
  // it's a string; otherwise fall back to the model id.
  const canonicalSlug =
    typeof model.canonical_slug === 'string' ? model.canonical_slug : '';
  const slugSource = (canonicalSlug || model.id) as string;
  const computedOpenrouterName = (slugSource.split('/').pop() || slugSource).trim();

  return {
    provider_id: providerId,
    name: model.id.split('/').pop()?.trim() || model.id,
    display_name: splitName.trim(),
    max_tokens: model.context_length ?? null,
    is_active: true,
    config: {} as Json,
    priority: null,
    tier: computeTier(model),
    allows_file_upload: allowsFileUpload,
    allows_search: Boolean(capabilitiesUnknown['web_search']),
    openrouter_name: computedOpenrouterName,
    is_visible_by_default: false,
    allows_tool_usage: allowsToolUsage,
    description: model.description || '',
    context_length: model.context_length ?? null,
    capabilities: (model.capabilities ?? {}) as Json,
    architecture: (model.architecture ?? {}) as Json,
    pricing: (model.pricing ?? {}) as Json,
    supported_parameters: (model.parameters ?? []) as Json,
    provider_specific_data: (model as unknown) as Json,
    last_synced: new Date().toISOString(),
  };
}

async function fetchOpenRouterModels(timeoutMs: number): Promise<OpenRouterModel[]> {
  const headers = buildHeaders();
  const url = 'https://openrouter.ai/api/v1/models';

  // Simple retry with backoff
  const attempts = 3;
  let lastErr: unknown;
  for (let i = 0; i < attempts; i++) {
    try {
      const res = await fetchWithTimeout(url, { headers, timeoutMs });
      if (!res.ok) {
        throw new Error(`Failed to fetch models: ${res.status} ${res.statusText}`);
      }
      const data = await res.json();
      const models = (data?.data ?? data?.models) as OpenRouterModel[] | undefined;
      if (!Array.isArray(models)) {
        throw new Error('Unexpected OpenRouter response shape; expected array at data or models');
      }
      return models;
    } catch (err) {
      lastErr = err;
      const delay = 500 * Math.pow(2, i);
      await new Promise((r) => setTimeout(r, delay));
    }
  }
  throw lastErr instanceof Error ? lastErr : new Error('Failed to fetch models');
}

async function main() {
  const opts = parseCliArgs(process.argv);
  const providerNames = opts.providers;
  if (!opts.all && providerNames.length === 0) {
    console.error(
      'Usage: npx ts-node scripts/sync-openrouter-models.ts [--all] [--group] [--update] [--format=json|ndjson] [--include-inactive] [--timeout=ms] [--providers=openai,anthropic] <provider1> <provider2> ...'
    );
    process.exit(1);
  }

  // Initialize Supabase (optional unless --update)
  const supabase = createSupabaseIfAvailable();
  if (opts.update && !supabase) {
    console.error(
      'Missing SUPABASE_URL and/or SUPABASE_SERVICE_ROLE_KEY environment variables required for --update'
    );
    process.exit(1);
  }

  // Fetch providers to get their IDs
  let providers: Array<{
    id: string;
    name: string;
    openrouter_name: string | null;
    is_active: boolean | null;
    supports_openrouter: boolean | null;
  }> = [];
  if (supabase) {
    providers = await fetchProviders(supabase, providerNames, opts.all);
  }

  const providerKeyToId = new Map<string, string>();
  for (const p of providers) {
    if (p.name) providerKeyToId.set(p.name.toLowerCase(), p.id);
    if (p.openrouter_name)
      providerKeyToId.set(p.openrouter_name.toLowerCase(), p.id);
  }

  const allModels = await fetchOpenRouterModels(opts.timeoutMs);

  const filteredModels = (opts.all ? allModels : allModels.filter((model) => {
    const provider = getProviderFromModel(model);
    return provider ? providerNames.includes(provider) : false;
  }))
    // Ensure consistent order
    .sort((a, b) => (a.name || a.id).localeCompare(b.name || b.id));

  // console.log(filteredModels);

  const mappedModels = filteredModels.map((model) => {
    const key = getProviderFromModel(model);
    const providerId = key ? providerKeyToId.get(key) ?? null : null;
    return mapModelToLLMModel(model, providerId);
  });
  const finalModels = opts.includeInactive
    ? mappedModels
    : mappedModels.map((m) => ({ ...m, is_active: true }));

  if (opts.group) {
    const grouped: Record<string, LLMModelInsert[]> = {};
    for (const model of finalModels) {
      const provider =
        getProviderFromIdentifier(model.openrouter_name || model.name) || 'unknown';
      if (!grouped[provider]) grouped[provider] = [];
      grouped[provider].push(model);
    }
    if (opts.format === 'ndjson') {
      for (const [provider, list] of Object.entries(grouped)) {
        for (const item of list) {
          process.stdout.write(
            JSON.stringify({ provider, model: item }) + '\n'
          );
        }
      }
    } else {
      console.log(JSON.stringify(grouped, null, 2));
    }
    return;
  }

  if (opts.format === 'ndjson') {
    for (const m of finalModels) {
      process.stdout.write(JSON.stringify(m) + '\n');
    }
  } else {
    console.log(JSON.stringify(finalModels, null, 2));
  }

  if (opts.update && supabase) {
    const { data, error } = await supabase
      .from('llm_models')
      .upsert(finalModels, {
        onConflict: 'provider_id,name',
        ignoreDuplicates: false,
        defaultToNull: false,
      })
      .select('id');
    if (error) {
      console.error('Upsert failed:', error);
      process.exit(1);
    }
    console.log(
      JSON.stringify(
        { updated_or_inserted: (data ?? []).length },
        null,
        2
      )
    );
  }
}

main().catch(err => {
  console.error(err);
  process.exit(1);
});