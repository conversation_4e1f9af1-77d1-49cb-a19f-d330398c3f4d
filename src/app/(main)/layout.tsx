'use client';
import { useEffect, useContext, useRef, Suspense } from 'react';
import { useRouter } from 'next/navigation';
import { ChatContext } from '@/providers/ChatProvider';
import { AppContext } from '@/providers/AppProvider';
import { GroupConversation } from '@/lib/supabase/types';
import { AppSidebar } from '@/components/sidebar/AppSidebar';
import { SidebarContext } from '@/providers/SidebarProvider';
import { useVisibilityChange } from '@/hooks/useVisibilityChange';

export default function ChatLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const mainContainerRef = useRef<HTMLDivElement>(null);
  const { fetchUserConversations } = useContext(ChatContext);
  const { isMobile, viewportHeight } = useContext(AppContext);
  const { sidebarState, setIsPeeking } = useContext(SidebarContext);

  // Initialize data on component mount
  useEffect(() => {
    fetchUserConversations(false); // Initial load - show full spinner
  }, [fetchUserConversations]);

  // Auto-refresh conversations when tab becomes visible
  useVisibilityChange(() => {
    fetchUserConversations(true); // Background refresh - subtle loading
  }, 1000); // 1 second debounce

  useEffect(() => {
    if (mainContainerRef.current) {
      mainContainerRef.current.style.height = `${viewportHeight}px`;
    }
  }, [viewportHeight]);
  useEffect(() => {
    if (isMobile && mainContainerRef.current) {
      mainContainerRef.current.style.height = `${viewportHeight}px`;
    }

    // Set initial height
    if (mainContainerRef.current) {
      mainContainerRef.current.style.height = isMobile
        ? `${viewportHeight}px`
        : '100svh';
    }
  }, [isMobile, viewportHeight]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Add global ⌘K shortcut to navigate to search page
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        router.push('/chat/search');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [router]);

  return (
    <div ref={mainContainerRef} className='flex flex-col h-screen bg-background'>
      {/* Peek Trigger Area */}
      {sidebarState === 'hidden' && (
        <div
          onMouseEnter={() => setIsPeeking(true)}
          className='fixed top-0 left-0 h-full w-2 z-50'
        />
      )}
      <div className='flex flex-1 overflow-hidden'>
        <AppSidebar
          goToConversation={(conversation: GroupConversation) => {
            router.push(`/c/${conversation.id}`);
          }}
        />
        <Suspense
          fallback={
            <div className="flex-1 overflow-auto p-6">
              <div className="h-8 w-2/3 rounded-md bg-muted animate-pulse mb-6" />
              <div className="grid gap-4 md:grid-cols-2">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="h-28 rounded-xl border border-border bg-card/70 backdrop-blur-sm animate-pulse" />
                ))}
              </div>
            </div>
          }
        >
          {children}
        </Suspense>
      </div>
    </div>
  );
}
