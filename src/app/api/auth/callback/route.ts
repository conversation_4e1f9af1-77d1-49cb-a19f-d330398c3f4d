import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { logger } from '@/lib/logger';
const log = logger.child({
  module: 'auth/callback',
});

export async function GET(request: Request) {
  const requestUrl = new URL(request.url);
  const searchParams = requestUrl.searchParams;

  const code = searchParams.get('code');
  const nextParam = searchParams.get('next') ?? '/';

  // Log safely: avoid leaking codes or full URLs
  log.info('auth callback', {
    origin: requestUrl.origin,
    hasCode: Boolean(code),
  });

  // Build a safe redirect target
  const origin = process.env.NEXT_PUBLIC_SITE_URL || requestUrl.origin;
  const safeNext = nextParam.startsWith('/') ? nextParam : '/';

  if (code) {
    const supabase = await createClient();
    const { error } = await supabase.auth.exchangeCodeForSession(code);

    if (!error) {
      const redirectUrl = new URL(safeNext, origin).toString();
      log.info('redirecting to', { to: redirectUrl });
      return NextResponse.redirect(redirectUrl);
    }
  }

  const errorRedirectUrl = new URL('/auth/auth-code-error', origin).toString();
  log.info('redirecting to error', { to: errorRedirectUrl });
  return NextResponse.redirect(errorRedirectUrl);
}
