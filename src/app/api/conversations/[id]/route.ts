import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/supabase/db';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';

export async function GET(
  _: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const conversation = await db.getConversation(id);
    if (!conversation || conversation.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Conversation not found or unauthorized' },
        { status: 404 }
      );
    }

    const conversationMessages = await db.getConversationMessages(id);
    return NextResponse.json({ data: conversationMessages });
  } catch (error) {
    const appError = handleError(error);
    const statusCode = appError.statusCode;
    return NextResponse.json({ error: appError.message }, { status: statusCode });
  }
}

export async function DELETE(
  _: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    if (!id) {
      return NextResponse.json(
        { error: 'Missing conversationId' },
        { status: 400 }
      );
    }
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const conversation = await db.getConversation(id);
    if (!conversation || conversation.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Conversation not found or unauthorized' },
        { status: 404 }
      );
    }

    await db.deleteConversation(id);
    return NextResponse.json({ success: true });
  } catch (error) {
    const appError = handleError(error);
    const statusCode = appError.statusCode;
    return NextResponse.json({ error: appError.message }, { status: statusCode });
  }
}
