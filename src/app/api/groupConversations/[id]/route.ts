import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/supabase/db';
import { logger } from '@/lib/logger';
import { createClient } from '@/utils/supabase/server';
import { AppError } from '@/lib/error';

const log = logger.child({
  module: 'group-conversation',
});

export async function GET(
  _: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const group = await db.getGroupConversation(id);
    if (!group || group.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Group conversation not found or unauthorized' },
        { status: 404 }
      );
    }

    const groupConversationMessages = await db.getGroupConversationMessages(id);
    const conversationData = groupConversationMessages.map((convo) => ({
      ...convo,
      parentMessageNode: db.buildMessageTree(convo.messages || []),
      messages: undefined,
    }));
    return NextResponse.json({ data: conversationData });
  } catch (error) {
    log.error({ err: error }, 'Error fetching group conversation messages');
    const statusCode = error instanceof AppError ? error.statusCode : 500;
    return NextResponse.json(
      { error: 'Failed to fetch group conversation messages' },
      { status: statusCode }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { title } = await request.json();
    if (!id || !title) {
      return NextResponse.json(
        { error: 'Missing conversationId or title' },
        { status: 400 }
      );
    }
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });

    const group = await db.getGroupConversation(id);
    if (!group || group.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Group conversation not found or unauthorized' },
        { status: 404 }
      );
    }

    await db.updateGroupConversation(id, { title });
    return NextResponse.json({ success: true });
  } catch (error) {
    log.error({ err: error }, 'Error updating group conversation');
    const statusCode = error instanceof AppError ? error.statusCode : 500;
    return NextResponse.json(
      { error: 'Failed to update group conversation' },
      { status: statusCode }
    );
  }
}

export async function PATCH(
  _: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    if (!id) {
      return NextResponse.json(
        { error: 'Missing conversationId' },
        { status: 400 }
      );
    }
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });

    const group = await db.getGroupConversation(id);
    if (!group || group.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Group conversation not found or unauthorized' },
        { status: 404 }
      );
    }

    await db.toggleGroupConversationFavorite(id);
    return NextResponse.json({ success: true });
  } catch (error) {
    log.error({ err: error }, 'Error toggling favorite status');
    const statusCode = error instanceof AppError ? error.statusCode : 500;
    return NextResponse.json(
      { error: 'Failed to toggle favorite status' },
      { status: statusCode }
    );
  }
}

export async function DELETE(
  _: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });

    const group = await db.getGroupConversation(id);
    if (!group || group.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Group conversation not found or unauthorized' },
        { status: 404 }
      );
    }

    await db.deleteGroupConversation(id);
    return NextResponse.json({ success: true });
  } catch (error) {
    log.error({ err: error }, 'Error deleting group conversation');
    const statusCode = error instanceof AppError ? error.statusCode : 500;
    return NextResponse.json(
      { error: 'Failed to delete group conversation' },
      { status: statusCode }
    );
  }
}
