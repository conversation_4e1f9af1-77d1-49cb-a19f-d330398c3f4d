import { DatabaseService } from '@/lib/supabase/db';
import { NextResponse, NextRequest } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';

export async function POST(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    if (!id) {
      return NextResponse.json(
        { error: 'Missing groupConversationId' },
        { status: 400 }
      );
    }

    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });

    const group = await db.getGroupConversation(id);
    if (!group || group.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Group conversation not found or unauthorized' },
        { status: 404 }
      );
    }

    await db.archiveGroupConversation(id);
    return NextResponse.json({ success: true });
  } catch (error) {
    const appError = handleError(error);
    const statusCode = appError.statusCode;
    return NextResponse.json({ error: appError.message }, { status: statusCode });
  }
}

export async function PUT(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    if (!id) {
      return NextResponse.json(
        { error: 'Missing groupConversationId' },
        { status: 400 }
      );
    }

    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });

    const group = await db.getGroupConversation(id);
    if (!group || group.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Group conversation not found or unauthorized' },
        { status: 404 }
      );
    }

    await db.restoreGroupConversation(id);
    return NextResponse.json({ success: true });
  } catch (error) {
    const appError = handleError(error);
    const statusCode = appError.statusCode;
    return NextResponse.json({ error: appError.message }, { status: statusCode });
  }
}
