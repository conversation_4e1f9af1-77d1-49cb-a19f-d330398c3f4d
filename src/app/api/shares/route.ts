import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { DatabaseService } from '@/lib/supabase/db';
import { withRateLimit } from '@/lib/rate-limit';

export const POST = withRateLimit(async (request: Request) => {

  try {
    const supabase = await createClient();
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { conversation_id, message_id, title } = await request.json();

    if (!conversation_id || !message_id) {
      return NextResponse.json(
        { error: 'Missing required fields: conversation_id and message_id' },
        { status: 400 }
      );
    }

    const db = DatabaseService.getInstance(supabase);

    // First, verify the conversation belongs to the user
    const conversation = await db.getConversation(conversation_id);
    if (!conversation || conversation.user_id !== session.user.id) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      );
    }

    // Create a new shared conversation
    const { data: sharedConversation, error } = await supabase
      .from('shared_conversations')
      .insert({
        original_conversation_id: conversation_id,
        shared_by_user_id: session.user.id,
        last_original_message_id: message_id,
        title: title || conversation.title || 'Shared Conversation',
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating shared conversation:', error);
      return NextResponse.json(
        { error: 'Failed to create shared conversation' },
        { status: 500 }
      );
    }

    // Get the message path from the original conversation
    const messages = await db.getConversationMessages(conversation_id);

    // Build a map of messages to quickly look up by id
    const messagesMap = new Map();
    messages.forEach((msg) => messagesMap.set(msg.id, msg));

    // Trace the path from the selected message back to the root
    const messagePath = [];
    let currentMessageId = message_id;

    while (currentMessageId) {
      const currentMessage = messagesMap.get(currentMessageId);
      if (!currentMessage) break;

      messagePath.unshift(currentMessage); // Add to the beginning to maintain order
      currentMessageId = currentMessage.parent_message_id || null;
    }

    // Create a mapping from original message IDs to shared message IDs
    const messageIdMap = new Map();

    // Copy all messages in the path to shared_messages
    for (const message of messagePath) {
      const { data: sharedMessage, error } = await supabase
        .from('shared_messages')
        .insert({
          shared_conversation_id: sharedConversation.id,
          original_message_id: message.id,
          parent_shared_message_id: message.parent_message_id
            ? messageIdMap.get(message.parent_message_id)
            : null,
          role: message.role,
          content: message.content,
          created_at: message.created_at,
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating shared message:', error);
        // Delete the shared conversation on failure
        await supabase
          .from('shared_conversations')
          .delete()
          .eq('id', sharedConversation.id);

        return NextResponse.json(
          { error: 'Failed to create shared messages' },
          { status: 500 }
        );
      }

      // Store the mapping from original to shared message IDs
      messageIdMap.set(message.id, sharedMessage.id);
    }

    return NextResponse.json({
      share_id: sharedConversation.id,
      share_url: `/share/${sharedConversation.id}`,
    });
  } catch (error) {
    console.error('Error in share creation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const conversationId = url.searchParams.get('conversation_id');

    if (!conversationId) {
      return NextResponse.json(
        { error: 'Missing required query parameter: conversation_id' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // Find existing share by conversation_id
    const { data, error } = await supabase
      .from('shared_conversations')
      .select('*')
      .eq('original_conversation_id', conversationId)
      .order('created_at', { ascending: false })
      .limit(1);

    if (error) {
      console.error('Error finding shared conversation:', error);
      return NextResponse.json(
        { error: 'Failed to find shared conversation' },
        { status: 500 }
      );
    }

    if (data && data.length > 0) {
      return NextResponse.json({
        share_id: data[0].id,
        share_url: `/share/${data[0].id}`,
        title: data[0].title,
        last_original_message_id: data[0].last_original_message_id,
      });
    }

    return NextResponse.json({ found: false });
  } catch (error) {
    console.error('Error finding share:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
