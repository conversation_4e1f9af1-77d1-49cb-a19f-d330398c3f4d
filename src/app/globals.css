@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Light theme colors */
  --color-white: #ffffff;
  --color-gray-100: #f8f9fa;
  --color-gray-200: #e9ecef;
  --color-gray-300: #dee2e6;
  --color-gray-400: #ced4da;
  --color-gray-500: #adb5bd;
  --color-gray-600: #6c757d;
  --color-gray-700: #495057;
  --color-gray-800: #343a40;
  --color-gray-900: #212529;
  --color-black: #000000;

  /* Destructive colors */
  --color-destructive: #dc2626;
  --color-destructive-hover: #b91c1c;
  --color-destructive-foreground: #ffffff;

  /* Primary accent color - a vibrant blue that complements the gray palette */
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;

  /* Light theme semantic colors */
  --color-background: var(--color-gray-100);
  --color-foreground: var(--color-gray-900);
  --color-muted: var(--color-gray-200);

  /* App structure */
  --color-sidebar-bg: var(--color-gray-200);
  --color-sidebar-fg: var(--color-gray-800);
  --color-sidebar-hover: var(--color-gray-300);
  --color-sidebar-active: var(--color-gray-400);

  /* Chat area */
  --color-chat-bg: var(--color-gray-100);
  --color-user-bubble-bg: var(--color-gray-300);
  --color-user-bubble-fg: var(--color-gray-900);
  --color-ai-bubble-bg: var(--color-gray-200);
  --color-ai-bubble-fg: var(--color-gray-800);

  /* Input areas */
  --color-input-bg: var(--color-white);
  --color-input-border: var(--color-gray-300);
  --color-input-fg: var(--color-gray-800);
  --color-input-placeholder: var(--color-gray-500);
  --color-input-focus-border: var(--color-primary-500);

  /* Buttons */
  --color-btn-primary-bg: var(--color-primary-600);
  --color-btn-primary-fg: var(--color-white);
  --color-btn-primary-hover: var(--color-primary-700);

  --color-btn-secondary-bg: var(--color-gray-300);
  --color-btn-secondary-fg: var(--color-gray-800);
  --color-btn-secondary-hover: var(--color-gray-400);

  /* Dividers, borders, etc */
  --color-border: var(--color-gray-300);
  --color-divider: var(--color-gray-200);

  /* Scrollbar */
  --color-scrollbar: var(--color-gray-400);
  --color-scrollbar-hover: var(--color-gray-500);
  --radius: 0.75rem;

  font-size: 16px;

  /* shadcn-compatible HSL tokens */
  --background: 0 0% 98%;
  --foreground: 224 71.4% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 224 71.4% 4.1%;
  --popover: 0 0% 100%;
  --popover-foreground: 224 71.4% 4.1%;
  --primary: 217 91% 60%;
  --primary-foreground: 210 40% 98%;
  --secondary: 220 14% 96%;
  --secondary-foreground: 220 9% 26%;
  --muted: 220 14% 96%;
  --muted-foreground: 220 9% 46%;
  --accent: 217 91% 60%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 220 13% 91%;
  --input: 220 13% 91%;
  --ring: 217 91% 60%;
  --chart-1: 217 91% 60%;
  --chart-2: 142 71% 45%;
  --chart-3: 31 97% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 346 77% 60%;
  --sidebar-background: 0 0% 100%;
  --sidebar-foreground: 224 71.4% 4.1%;
  --sidebar-primary: 217 91% 60%;
  --sidebar-primary-foreground: 210 40% 98%;
  --sidebar-accent: 220 14% 96%;
  --sidebar-accent-foreground: 220 9% 26%;
  --sidebar-border: 220 13% 91%;
  --sidebar-ring: 217 91% 60%;
}

.dark {
  /* Dark theme semantic colors */
  --color-background: var(--color-gray-900);
  --color-foreground: var(--color-gray-100);
  --color-muted: var(--color-gray-800);

  /* Destructive colors */
  --color-destructive: #ef4444;
  --color-destructive-hover: #dc2626;
  --color-destructive-foreground: #ffffff;

  /* App structure */
  --color-sidebar-bg: var(--color-gray-800);
  --color-sidebar-fg: var(--color-gray-200);
  --color-sidebar-hover: var(--color-gray-700);
  --color-sidebar-active: var(--color-gray-600);

  /* Chat area */
  --color-chat-bg: var(--color-gray-900);
  --color-user-bubble-bg: var(--color-gray-700);
  --color-user-bubble-fg: var(--color-gray-100);
  --color-ai-bubble-bg: var(--color-gray-800);
  --color-ai-bubble-fg: var(--color-gray-200);

  /* Input areas */
  --color-input-bg: var(--color-gray-800);
  --color-input-border: var(--color-gray-700);
  --color-input-fg: var(--color-gray-200);
  --color-input-placeholder: var(--color-gray-500);
  --color-input-focus-border: var(--color-primary-500);

  /* Buttons */
  --color-btn-primary-bg: var(--color-primary-600);
  --color-btn-primary-fg: var(--color-white);
  --color-btn-primary-hover: var(--color-primary-700);

  --color-btn-secondary-bg: var(--color-gray-700);
  --color-btn-secondary-fg: var(--color-gray-200);
  --color-btn-secondary-hover: var(--color-gray-600);

  /* Dividers, borders, etc */
  --color-border: var(--color-gray-700);
  --color-divider: var(--color-gray-800);

  /* Scrollbar */
  --color-scrollbar: var(--color-gray-700);
  --color-scrollbar-hover: var(--color-gray-600);
  --radius: 0.75rem;

  /* shadcn-compatible HSL tokens (dark) */
  --background: 224 71.4% 4.1%;
  --foreground: 210 20% 98%;
  --card: 224 71.4% 4.1%;
  --card-foreground: 210 20% 98%;
  --popover: 224 71.4% 4.1%;
  --popover-foreground: 210 20% 98%;
  --primary: 217 91% 60%;
  --primary-foreground: 210 40% 98%;
  --secondary: 215 27.9% 16.9%;
  --secondary-foreground: 210 40% 98%;
  --muted: 215 27.9% 16.9%;
  --muted-foreground: 217.9 10.6% 64.9%;
  --accent: 217 91% 60%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 53.9%;
  --destructive-foreground: 210 40% 98%;
  --border: 215 27.9% 16.9%;
  --input: 215 27.9% 16.9%;
  --ring: 224 71.4% 4.1%;
  --sidebar-background: 224 71.4% 4.1%;
  --sidebar-foreground: 210 20% 98%;
  --sidebar-primary: 217 91% 60%;
  --sidebar-primary-foreground: 210 40% 98%;
  --sidebar-accent: 215 27.9% 16.9%;
  --sidebar-accent-foreground: 210 40% 98%;
  --sidebar-border: 215 27.9% 16.9%;
  --sidebar-ring: 217 91% 60%;
}

/* Base styles */
body {
  background-color: var(--color-background);
  color: var(--color-foreground);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Component classes */
@layer components {
  /* Layout components */
  .sidebar {
    @apply bg-sidebar-bg text-sidebar-fg border-r border-border;
  }

  .sidebar-item {
    @apply py-2 px-4 hover:bg-sidebar-hover rounded-md transition-colors duration-150 ease-in-out;
  }

  .sidebar-item-active {
    @apply bg-sidebar-active font-medium;
  }

  .main-content {
    @apply bg-background text-foreground;
  }

  /* Chat components */
  .chat-container {
    @apply bg-chat-bg p-4 overflow-y-auto;
  }

  .message-bubble-user {
    @apply bg-user-bubble-bg text-user-bubble-fg p-3 rounded-lg rounded-tr-none max-w-[80%] ml-auto mb-4 shadow-sm;
  }

  .message-bubble-ai {
    @apply bg-ai-bubble-bg text-ai-bubble-fg p-3 rounded-lg rounded-tl-none max-w-[80%] mr-auto mb-4 shadow-sm;
  }

  .message-timestamp {
    @apply text-xs text-foreground/50 mt-1;
  }

  /* Input components */
  .input-base {
    @apply bg-input-bg border border-input-border text-input-fg placeholder:text-input-placeholder
           rounded-md p-2 focus:border-input-focus-border focus:outline-none focus:ring-2
           focus:ring-primary/20 transition-all duration-150 ease-in-out;
  }

  .chat-input {
    @apply input-base w-full resize-none min-h-[50px];
  }

  /* Button components */
  .btn-primary {
    @apply bg-btn-primary-bg text-btn-primary-fg px-4 py-2 rounded-md
           hover:bg-btn-primary-hover transition-all duration-150 ease-in-out font-medium
           shadow-sm hover:shadow active:translate-y-px;
  }

  .btn-secondary {
    @apply bg-btn-secondary-bg text-btn-secondary-fg px-4 py-2 rounded-md
           hover:bg-btn-secondary-hover transition-colors duration-150 ease-in-out font-medium;
  }

  .btn-icon {
    @apply p-2 rounded-full hover:bg-sidebar-hover transition-colors duration-150 ease-in-out;
  }

  /* Other components */
  .divider {
    @apply h-px w-full bg-divider my-4;
  }

  /* Frosted glass surface for sticky bars and headers */
  .glass-panel {
    @apply bg-background/80 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-t border-border;
  }

  /* Table styles */
  .markdown-table-wrapper {
    @apply overflow-x-auto mb-4 border border-border rounded-md;
  }

  .markdown-table {
    @apply min-w-full border-collapse;
  }

  .markdown-table thead {
    @apply bg-muted;
  }

  .markdown-table tbody {
    @apply divide-y divide-border;
  }

  .markdown-table tr {
    @apply hover:bg-sidebar-hover transition-colors duration-100;
  }

  .markdown-table th {
    @apply border-b border-border px-4 py-2 text-left font-semibold;
  }

  .markdown-table td {
    @apply border-border px-4 py-2;
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--color-scrollbar);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-scrollbar-hover);
}

/* Extend Tailwind utility classes with our custom colors */
@layer utilities {
  .bg-background { background-color: var(--color-background); }
  .bg-foreground { background-color: var(--color-foreground); }
  .bg-muted { background-color: var(--color-muted); }
  .bg-sidebar-bg { background-color: var(--color-sidebar-bg); }
  .bg-sidebar-hover { background-color: var(--color-sidebar-hover); }
  .bg-sidebar-active { background-color: var(--color-sidebar-active); }
  .bg-chat-bg { background-color: var(--color-chat-bg); }
  .bg-user-bubble-bg { background-color: var(--color-user-bubble-bg); }
  .bg-ai-bubble-bg { background-color: var(--color-ai-bubble-bg); }
  .bg-input-bg { background-color: var(--color-input-bg); }
  .bg-btn-primary-bg { background-color: var(--color-btn-primary-bg); }
  .bg-btn-primary-hover { background-color: var(--color-btn-primary-hover); }
  .bg-btn-secondary-bg { background-color: var(--color-btn-secondary-bg); }
  .bg-btn-secondary-hover { background-color: var(--color-btn-secondary-hover); }
  .bg-primary { background-color: var(--color-primary-500); }
  .bg-primary-dark { background-color: var(--color-primary-600); }
  .bg-destructive { background-color: var(--color-destructive); }
  .bg-destructive-hover { background-color: var(--color-destructive-hover); }
  .bg-divider { background-color: var(--color-divider); }

  .text-foreground { color: var(--color-foreground); }
  .text-sidebar-fg { color: var(--color-sidebar-fg); }
  .text-user-bubble-fg { color: var(--color-user-bubble-fg); }
  .text-ai-bubble-fg { color: var(--color-ai-bubble-fg); }
  .text-input-fg { color: var(--color-input-fg); }
  .text-input-placeholder { color: var(--color-input-placeholder); }
  .text-btn-primary-fg { color: var(--color-btn-primary-fg); }
  .text-btn-secondary-fg { color: var(--color-btn-secondary-fg); }
  .text-primary { color: var(--color-primary-500); }
  .text-destructive-foreground { color: var(--color-destructive-foreground); }

  .border-border { border-color: var(--color-border); }
  .border-input-border { border-color: var(--color-input-border); }
  .border-input-focus-border { border-color: var(--color-input-focus-border); }
  .border-primary { border-color: var(--color-primary-500); }

  .ring-primary { --tw-ring-color: var(--color-primary-500); }

  /* Hero glow background utility */
  .bg-hero-glow {
    background:
      radial-gradient(1200px circle at 50% -200px, hsl(var(--primary) / 0.12), transparent 60%),
      radial-gradient(800px circle at 90% 120%, hsl(var(--primary) / 0.08), transparent 60%);
  }

  /* Hide scrollbars but keep scrolling functionality */
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari */
  }
}

@source '../node_modules/@ferrucc-io/emoji-picker';
