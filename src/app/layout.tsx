import React from 'react';
import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON>ei<PERSON>, <PERSON>ei<PERSON>_Mono } from 'next/font/google';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/sonner';
import { ChatProvider } from '@/providers/ChatProvider';

import './globals.css';
import AuthProvider from '@/providers/AuthProvider';
import { SidebarProvider } from '@/providers/SidebarProvider';
import { CodeThemeProvider as SyntaxThemeProvider } from '@/providers/CodeThemeProvider';
import { AppProvider } from '@/providers/AppProvider';
import { FeatureFlagsProvider } from '@/providers/FeatureFlagProvider';
import ShareImportHandler from '@/components/ShareImportHandler';
import AnalyticsProvider from '@/providers/AnalyticsProvider';

import { BrowserTelemetry } from '@/services/instrumentation/honeycombClient';
import { NavigationHistoryProvider } from '@/providers/NavigationHistoryProvider';
import { <PERSON><PERSON>rovider } from '@reactour/tour';


const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Sabi Chat',
  description: 'Sabi de ting',
  icons: {
    icon: '/favicon.ico',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <BrowserTelemetry />
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <AppProvider>
          <NavigationHistoryProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="system"
              enableSystem
              disableTransitionOnChange
              storageKey="theme"
            >
              <SyntaxThemeProvider>
                <AuthProvider>
                  <FeatureFlagsProvider>
                    <ChatProvider>
                      <SidebarProvider>
                        <TourProvider>
                        <AnalyticsProvider>
                          <ShareImportHandler />
                          {children}
                        </AnalyticsProvider>
                        </TourProvider>
                      </SidebarProvider>
                    </ChatProvider>
                    <Toaster />
                  </FeatureFlagsProvider>
                </AuthProvider>
              </SyntaxThemeProvider>
            </ThemeProvider>
          </NavigationHistoryProvider>
        </AppProvider>
      </body>
    </html>
  );
}
