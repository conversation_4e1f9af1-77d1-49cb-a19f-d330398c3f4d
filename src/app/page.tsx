import Link from 'next/link'
import { Button } from '@/components/ui/button'

export default function Home() {
  return (
    <main className="relative min-h-[100svh] overflow-hidden bg-hero-glow">
      <div className="pointer-events-none absolute -top-40 left-1/2 h-[600px] w-[1200px] -translate-x-1/2 rounded-full bg-primary/10 blur-3xl" />

      <section className="mx-auto flex max-w-5xl flex-col items-center px-6 py-24 text-center sm:py-32">
        <span className="mb-4 inline-flex items-center rounded-full border border-border bg-card/60 px-3 py-1 text-xs text-muted-foreground backdrop-blur">
          New: Faster model switching and cleaner chat UI
        </span>
        <h1 className="mb-4 text-balance text-4xl font-semibold tracking-tight sm:text-5xl md:text-6xl">
          Sabi Chat
        </h1>
        <p className="mb-8 max-w-2xl text-pretty text-muted-foreground sm:text-lg">
          Simple, powerful AI chat for work. Keep your focus, switch models instantly, and share results with ease.
        </p>
        <div className="flex flex-wrap items-center justify-center gap-3">
          <Button asChild size="lg">
            <Link href="/chat">Open Chat</Link>
          </Button>
          <Button asChild size="lg" variant="outline">
            <Link href="/settings">Settings</Link>
          </Button>
        </div>
      </section>
    </main>
  )
}
