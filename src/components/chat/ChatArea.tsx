import React, {
    useState,
    useRef,
    useMemo,
    useContext,
    useCallback,
    useEffect,
} from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { MessageNode } from '@/lib/supabase/types';
import ChatContent from './ChatContent';
import ChatInput from './ChatInput';
import { ChatContext } from '@/providers/ChatProvider';
import { AppContext } from '@/providers/AppProvider';
import { useChatHistory } from './hooks/useChatHistory';
import { useChatModels } from './hooks/useChatModels';
import { useMessageTree } from './hooks/useMessageTree';
import { useSendMessage } from './hooks/useSendMessage';
import { usePartialMessages } from './hooks/usePartialMessages';
import { WorkspaceInfo } from '@/hooks/useWorkspaceInfo';
import { useSubscription } from '@/providers/SubscriptionProvider';
import { ModelSelectionIndicator } from './ModelSelectionIndicator';

// Significantly reduced prop interface// 1) Common fields
interface BaseProps {
  groupConversationId?: string;
  isTemporary: boolean;
  setIsTemporary: React.Dispatch<React.SetStateAction<boolean>>;
  chatState: 'chat' | 'workspace';
  onMessageSent?: () => void;
  selectedWorkspaceInfo?: WorkspaceInfo | null;
  initialPrompt?: string;
  initialDefaultModel?: string;
}

// 2a) Props for the "chat" mode
interface ChatOnlyProps extends BaseProps {
  chatState: 'chat';
  // no extra fields
}

// 2b) Props for the "workspace" mode
interface WorkspaceProps extends BaseProps {
  chatState: 'workspace';
  workspaceId: string;
}

// 3) Discriminated union
type Props = ChatOnlyProps | WorkspaceProps;

// Using React.memo to prevent unnecessary re-renders
const ChatArea = React.memo((props: Props) => {
  const {
    groupConversationId,
    isTemporary,
    setIsTemporary,
    chatState,
    onMessageSent,
    initialPrompt,
    initialDefaultModel,
  } = props;
  const workspaceId =
    chatState === 'workspace'
      ? (props as WorkspaceProps).workspaceId
      : props.selectedWorkspaceInfo?.id;
  const { providers, chatSessions, setChatSessions, partialMessages } =
    useContext(ChatContext);
  const { isTestMode, isMobile } = useContext(AppContext);
  const { accessibleModelIds } = useSubscription();
  const [selectedWorkspaceId, setSelectedWorkspaceId] = useState<string | null>(
    workspaceId || null
  );

  useEffect(() => {
    if (!selectedWorkspaceId) {
      setSelectedWorkspaceId(workspaceId || null);
    }
  }, [workspaceId, selectedWorkspaceId]);

  // Handle initial default model from prompt
  useEffect(() => {
    if (
      initialDefaultModel &&
      initialDefaultModel.trim() !== '' &&
      providers.length > 0
    ) {
      // Find the model and provider
      const findModelAndProvider = (modelId: string) => {
        for (const provider of providers) {
          const model = provider.models.find((m) => m.id === modelId);
          if (model) {
            return model;
          }
        }
        return null;
      };

      // Check if the prompt's default model is accessible
      if (accessibleModelIds.includes(initialDefaultModel)) {
        const modelToUse = findModelAndProvider(initialDefaultModel);
        if (modelToUse) {
          // Update the chat session with the prompt's default model
          setChatSessions((prev) => {
            if (prev.length === 0) return prev;
            const updated = [...prev];
            updated[0] = {
              ...updated[0],
              model: modelToUse,
            };
            return updated;
          });
        }
      }
    }
  }, [initialDefaultModel, providers, accessibleModelIds, setChatSessions]);

  const [inputMessage, setInputMessage] = useState<string>(initialPrompt || '');
  const inputContainerRef = useRef<HTMLDivElement | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement | null>(null);

  const isChatDirty = useRef(false);
  const { isLoading: isLoadingConversationHistory } = useChatHistory({
    chatSessions,
    setChatSessions,
    groupConversationId,
  });

  // Only show the temporary toggle when creating a new chat (no groupConversationId)
  const showTemporaryToggle = !groupConversationId;

  // Use the enhanced capability checking from useChatModels hook
  const {
    canSearch,
    canUpload,
    useWebSearch,
    setUseWebSearch,
    canGenerateImages,
    useImageGeneration,
    setUseImageGeneration,
    checkAllModelsSupport,
    modelSupportsCapability,
  } = useChatModels();

  const { selectedBranches, getFlattenedMessages, handleBranchChange } = useMessageTree();

  // Use the hook to ensure partial messages are applied when returning to a chat
  usePartialMessages({
    chatSessions,
    getFlattenedMessages,
  });

  const {
    handleSendMessage: originalHandleSendMessage,
    handleRetryMessage,
    handleUpload,
    isUploading,
    processingModels,
    editMessage,
    handleRemoveFile,
  } = useSendMessage({
    chatSessions,
    setChatSessions,
    groupConversationId,
    isTestMode,
    getFlattenedMessages,
    providers,
    setInputMessage,
    useWebSearch,
    useImageGeneration,
    isTemporary,
    workspaceId: selectedWorkspaceId || undefined,
    onMessageSent,
  });

  const handleSendMessage = useCallback(async () => {
    isChatDirty.current = true;
    if (isMobile) {
      textareaRef.current?.blur();
    }
    const message = inputMessage;
    await originalHandleSendMessage(message);
  }, [originalHandleSendMessage, inputMessage, isMobile]);

  const isStreamingOrUploading = useMemo(() => {
    // Only consider current chat sessions as streaming, not all processing models globally
    const currentSessionIds = new Set(chatSessions.map(session => session.id));
    const isCurrentChatStreaming = processingModels.some(modelId =>
      currentSessionIds.has(modelId)
    );
    return isUploading || isCurrentChatStreaming;
  }, [isUploading, processingModels, chatSessions]);

  // We need to compute flattenedMessages for the ChatContent component
  // Now includes partial message content from the context
  const flattenedMessages = useMemo(() => {
    const result: Record<string, MessageNode[]> = {};

    chatSessions.forEach((model) => {
      const messages = getFlattenedMessages(model.parentMessageNode);

      // Add partial messages to the flattened message list
      // This ensures that if a user navigates away during streaming
      // and returns, they'll see all message content received so far
      if (messages.length > 0) {
        const lastMessage = messages[messages.length - 1];
        if (lastMessage && lastMessage.role === 'assistant') {
          const partialMessage = partialMessages.get(lastMessage.id);
          if (partialMessage && partialMessage.content) {
            // Check if partial message is longer than what's in the tree
            // This ensures we always show the most complete version
            const usePartialContent =
              partialMessage.content.length >= lastMessage.content.length;

            // Create a copy of the message with the most complete content
            messages[messages.length - 1] = {
              ...lastMessage,
              content: usePartialContent
                ? partialMessage.content
                : lastMessage.content,
              annotations: partialMessage.annotations?.length
                ? partialMessage.annotations
                : lastMessage.annotations,
            };
          }
        }
      }

      result[model.id] = messages;
    });

    return result;
  }, [chatSessions, getFlattenedMessages, partialMessages]);

  const showInputInBottom = useMemo(() => {
    return (
      isLoadingConversationHistory ||
      chatState === 'workspace' ||
      chatSessions.length > 1 ||
      chatSessions.some((model) => flattenedMessages[model.id]?.length > 0)
    );
  }, [chatSessions, flattenedMessages, isLoadingConversationHistory]);

  return (
    <div className='flex-1 flex flex-col overflow-hidden h-full' data-tour="chat-area">
      {isLoadingConversationHistory ? (
        <div className='bg-chat-bg h-full' />
      ) : (
        <ChatContent
          processingModels={processingModels}
          handleRetryMessage={handleRetryMessage}
          isChatDirty={isChatDirty.current}
          flattenedMessages={flattenedMessages}
          handleBranchChange={handleBranchChange}
          isChatLoading={isLoadingConversationHistory}
          editMessage={editMessage}
          workspaceId={selectedWorkspaceId || undefined}
          selectedBranches={selectedBranches}
          rootMessage={chatSessions[0]?.parentMessageNode || null}
        />
      )}

      <ModelSelectionIndicator />

      {chatSessions.some((model) => model.conversationState !== 'error') && (
        <motion.div
          className='p-4 bg-background text-foreground w-full'
          initial={false}
          animate={{
            position: showInputInBottom ? 'relative' : 'absolute',
            bottom: 0,
            top: showInputInBottom ? 'auto' : '30%',
          }}
          transition={{ duration: 0.01, ease: 'easeInOut' }}
        >
          {!showInputInBottom && (
            <div className='flex flex-col items-center justify-center mb-6'>
              <Image
                src='/logo/logo5.png'
                alt='App Logo'
                width={100}
                height={100}
              />
              <p className='text-lg text-muted-foreground'>
                How can I help you?
              </p>
            </div>
          )}
          {isUploading && (
            <div className='text-center text-xs text-muted-foreground mb-1'>
              Uploading files...
            </div>
          )}
          <ChatInput
            inputValue={inputMessage}
            setInputValue={setInputMessage}
            handleSubmit={handleSendMessage}
            handleInputContainerClick={() => {}}
            isStreaming={isStreamingOrUploading}
            inputContainerRef={inputContainerRef}
            textareaRef={textareaRef}
            handleUpload={handleUpload}
            canUpload={canUpload}
            canSearch={canSearch}
            useWebSearch={useWebSearch}
            setUseWebSearch={setUseWebSearch}
            canGenerateImages={canGenerateImages}
            useImageGeneration={useImageGeneration}
            setUseImageGeneration={setUseImageGeneration}
            isTemporary={isTemporary}
            setIsTemporary={setIsTemporary}
            showTemporaryToggle={showTemporaryToggle}
            handleRemoveFile={handleRemoveFile}
            canSelectWorkspace
            selectedWorkspaceId={selectedWorkspaceId}
            setSelectedWorkspaceId={setSelectedWorkspaceId}
            checkAllModelsSupport={checkAllModelsSupport}
            modelSupportsCapability={modelSupportsCapability}
          />
        </motion.div>
      )}
    </div>
  );
});

ChatArea.displayName = 'ChatArea';

export default ChatArea;
