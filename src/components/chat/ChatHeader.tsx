'use client';
import { useContext } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  PlusCircle,
  Star,
  Clock,
  AlignJustify,
  ExternalLink,
} from 'lucide-react';
import { ChatContext } from '@/providers/ChatProvider';
import { SidebarContext } from '@/providers/SidebarProvider';
import { useRouter } from 'next/navigation';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { AppContext } from '@/providers/AppProvider';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

import { useAnalytics } from '@/hooks/useAnalytics';
import ModelSelector from './ChatModelSelector/ModelSelector';
import { isMobile } from 'react-device-detect';
import { WorkspaceInfo } from '@/hooks/useWorkspaceInfo';

const MAX_TITLE_LENGTH_DESKTOP = 30;
const MAX_TITLE_LENGTH_MOBILE = 20;

const truncateTitle = (title: string, isMobileView: boolean = false) => {
  const maxLength = isMobileView
    ? MAX_TITLE_LENGTH_MOBILE
    : MAX_TITLE_LENGTH_DESKTOP;
  return title.length > maxLength ? title.slice(0, maxLength) + '...' : title;
};

export default function ChatHeader({
  isTemporary,
  conversationId,
  workspace,
}: {
  isTemporary: boolean;
  conversationId: string;
  workspace?: WorkspaceInfo | null;
}) {
  const { initializeNewChat, selectedConversation } = useContext(ChatContext);
  const { isTestMode, setIsTestMode } = useContext(AppContext);
  const router = useRouter();
  const analytics = useAnalytics();

  const { toggleSidebarState } = useContext(SidebarContext);

  const handleNewConversation = () => {
    initializeNewChat();
    router.push('/chat');
  };

  const handleWorkspaceClick = () => {
    if (workspace) {
      router.push(`/workspaces/${workspace.id}`);
    }
  };

  const currentTitle = truncateTitle(
    selectedConversation?.title || 'New Chat',
    isMobile
  );

  const toggleFavorite = async () => {
    if (!conversationId || !selectedConversation) return;

    try {
      const response = await fetch(
        `/api/groupConversations/${conversationId}`,
        {
          method: 'PATCH',
        }
      );

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to toggle favorite status');
      }

      // Track favoriting from the header
      analytics.trackConversationFavorited(
        conversationId,
        !selectedConversation.is_favorite
      );

      // The UI will update on its own through context
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  return (
    <div className='grid grid-cols-3 items-center px-4 py-3 gap-4 border-b border-border min-h-[60px]'>
      {/* Left section - sidebar toggle, title, workspace, favorite */}
      <div className='flex items-center gap-2 min-w-0'>
        <button
          className='text-btn-secondary-fg rounded-md p-2 hover:bg-btn-secondary-hover flex-shrink-0'
          onClick={toggleSidebarState}
          aria-label='Show sidebar'
        >
          <AlignJustify className='h-5 w-5' />
        </button>

        <div className='flex items-center gap-2 min-w-0 flex-1'>
          {/* Title with responsive sizing */}
          <h2
            className='font-medium text-foreground truncate min-w-0 text-sm sm:text-base max-w-[100px] sm:max-w-[160px] md:max-w-[200px]'
            title={selectedConversation?.title || 'Sabi Chat'}
          >
            {currentTitle}
          </h2>

          {/* Workspace Indicator - more compact on mobile */}
          {conversationId && workspace && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={handleWorkspaceClick}
                    className='flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full border border-primary/30 bg-primary/5 text-primary hover:bg-primary/10 transition-colors text-xs font-medium shadow-sm flex-shrink-0'
                  >
                    <div className='h-4 w-4 sm:h-5 sm:w-5 rounded-full bg-primary/20 flex items-center justify-center text-xs font-semibold flex-shrink-0'>
                      {workspace.icon || workspace.name.charAt(0).toUpperCase()}
                    </div>
                    <span className='hidden md:inline truncate max-w-[80px] lg:max-w-[120px]'>
                      {workspace.name}
                    </span>
                    <ExternalLink className='h-3 w-3 flex-shrink-0' />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Open {workspace.name} workspace</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          {/* Favorite button */}
          {selectedConversation && (
            <button
              className='text-foreground/70 hover:text-foreground transition-colors flex-shrink-0 p-1 hover:bg-muted rounded-md'
              onClick={toggleFavorite}
              aria-label={
                selectedConversation.is_favorite
                  ? 'Remove from favorites'
                  : 'Add to favorites'
              }
            >
              <Star
                className={`h-4 w-4 sm:h-5 sm:w-5 ${
                  selectedConversation.is_favorite
                    ? 'fill-yellow-400 text-yellow-400'
                    : 'fill-none'
                }`}
              />
            </button>
          )}
        </div>
      </div>

      {/* Center section - model selector (properly centered) */}
      <div className='flex justify-center items-center min-w-0'>
        <div className='w-full max-w-[200px] sm:max-w-[250px]'>
          <ModelSelector />
        </div>
      </div>

      {/* Right section - actions */}
      <div className='flex items-center justify-end gap-1 sm:gap-2'>
        {process.env.NODE_ENV === 'development' && (
          <div className='flex items-center gap-1 sm:gap-2'>
            <Switch
              checked={isTestMode}
              onCheckedChange={setIsTestMode}
              className='scale-75 sm:scale-100'
            />
            <Label className='hidden md:inline text-xs sm:text-sm'>
              Test Mode
            </Label>
          </div>
        )}

        {isTemporary && (
          <div className='flex items-center gap-1 text-xs px-1.5 sm:px-2 py-0.5 bg-amber-100 text-amber-700 dark:bg-amber-900 dark:text-amber-300 rounded-full flex-shrink-0'>
            <Clock className='h-3 w-3' />
            <span className='hidden sm:inline'>Temp</span>
          </div>
        )}

        {/* New Chat Button - responsive design */}
        <Button
          variant='default'
          size={isMobile ? 'sm' : 'default'}
          className='bg-primary hover:bg-primary/90 hover:bg-btn-primary-hover flex-shrink-0'
          onClick={handleNewConversation}
          aria-label='New Chat'
        >
          <PlusCircle className='h-4 w-4' />
          <span className='hidden sm:inline ml-2'>New Chat</span>
        </Button>
      </div>
    </div>
  );
}
