'use client';
import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { logger } from '@/lib/logger';
import { AttachmentPayload } from '@/lib/supabase/types';

interface FilePreview {
  name: string;
  type: string;
  size: number;
  file: File;
}

const log = logger.child({ hook: 'useAttachmentUpload' });

// Function to calculate SHA-256 hash
const calculateHash = async (file: File) => {
  const buffer = await file.arrayBuffer();
  const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map((b) => b.toString(16).padStart(2, '0')).join('');
};

// Check if file already exists in the system
const checkFileExists = async (hash: string) => {
  try {
    const response = await fetch('/api/upload/check-file', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ hash }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to check file');
    }

    return await response.json();
  } catch (error) {
          log.error({ err: error, hash, skipSentry: true }, 'Error checking file existence');
    return { exists: false };
  }
};

export const useAttachmentUpload = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [attachments, setAttachments] = useState<AttachmentPayload[]>([]);

  const handleUpload = useCallback(
    async (attachmentsToUpload?: FilePreview[]) => {
      if (!attachmentsToUpload || attachmentsToUpload.length === 0) return;

      const filtered = attachmentsToUpload.filter(
        (newFile) => !attachments.some((f) => f.name === newFile.name)
      );
      if (filtered.length === 0) return;

      let payloads: AttachmentPayload[] | undefined;
      setIsUploading(true);

      try {
        // Calculate hashes and check existence
        const fileChecks = await Promise.all(
          filtered.map(async (preview) => {
            try {
              const hash = await calculateHash(preview.file);
              log.info(
                { fileName: preview.name, hash },
                'Calculated file hash'
              );
              const { exists, file } = await checkFileExists(hash);
              return { preview, hash, exists, existingFile: file };
            } catch (error) {
              log.error(
                { error, fileName: preview.name },
                'Error processing file'
              );
              return {
                preview,
                hash: null,
                exists: false,
                existingFile: null,
                error,
              };
            }
          })
        );

        const uploadResults = await Promise.all(
          fileChecks.map(
            async ({ preview, hash, exists, existingFile, error }) => {
              if (error || !hash) {
                toast.error(
                  `Error processing ${preview.name}: ${
                    error instanceof Error ? error.message : 'Unknown error'
                  }`
                );
                return null;
              }
              if (exists && existingFile) {
                log.info(
                  { fileName: preview.name, hash },
                  'File exists, reusing'
                );
                return {
                  name: preview.name,
                  type: preview.type,
                  url: existingFile.url || '',
                };
              }
              const formData = new FormData();
              formData.append('file', preview.file);
              formData.append('hash', hash);
              const response = await fetch('/api/upload', {
                method: 'POST',
                body: formData,
              });
              if (!response.ok) {
                const errorData = await response.json();
                throw new Error(
                  errorData.error || `Failed to upload ${preview.name}`
                );
              }
              const result = await response.json();
              return {
                name: preview.name,
                type: preview.type,
                url: result.url,
              };
            }
          )
        );

        const successful = uploadResults.filter(
          (r): r is AttachmentPayload => r !== null
        );
        if (successful.length > 0) {
          payloads = successful;
          setAttachments((prev) =>
            Array.from(
              new Map([...prev, ...successful].map((f) => [f.name, f])).values()
            )
          );
        }
      } catch (error) {
        log.error({ err: error, skipSentry: true }, 'Error in file upload process');
        toast.error('Failed to process file uploads. Please try again.');
      } finally {
        setIsUploading(false);
      }

      return payloads;
    },
    [attachments]
  );

  const handleRemoveFile = useCallback((fileName: string) => {
    setAttachments((prev) => prev.filter((f) => f.name !== fileName));
  }, []);

  // Clear all attachments
  const clearAttachments = useCallback(() => {
    setAttachments([]);
  }, []);

  return {
    attachments,
    handleUpload,
    isUploading,
    handleRemoveFile,
    clearAttachments,
  };
};
