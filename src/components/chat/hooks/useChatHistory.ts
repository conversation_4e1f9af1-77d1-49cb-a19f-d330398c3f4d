import { useCallback, useEffect, useContext, useState, useRef } from 'react';
import {
  ConversationWithParentMessageNode,
  ConversationState,
  Provider,
  MessageNode,
  Model,
} from '@/lib/supabase/types';
import { logger } from '@/lib/logger';
import { ChatSession, ChatContext } from '@/providers/ChatProvider';
const log = logger.child({
  module: 'useChatHistory',
});

export function useChatHistory({
  setChatSessions,
  groupConversationId,
  chatSessions,
}: {
  setChatSessions: (sessions: ChatSession[]) => void;
  groupConversationId?: string;
  chatSessions: ChatSession[];
}) {
  const {
    loadedConversations,
    setSelectedConversation,
    initializeNewChat,
    providers,
    appDefaultModel,
  } = useContext(ChatContext);

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const fetchedRef = useRef(new Set<string>());

  // Fetch conversation history for a group
  const fetchConversationHistory = useCallback(
    async (groupId: string, providers: Provider[]) => {
      if (!groupId) return;

      try {
        setIsLoading(true);
        setError(null);
        const response = await fetch(`/api/groupConversations/${groupId}`);

        if (!response.ok) {
          throw new Error(
            `Failed to fetch conversation history: ${response.status}`
          );
        }

        const conversationsData: ConversationWithParentMessageNode[] =
          await response.json();

        // Sort by comparison index and transform to ChatSession format
        const sessionsData = conversationsData
          .sort((a, b) => (a.comparison_index ?? 0) - (b.comparison_index ?? 0))
          .map((conversation) => {
            // Find model for this conversation from providers
            const flatProviders = providers.flatMap((p) => p.models);
            let modelToUse = flatProviders.find(
              (m) => m.id === conversation.last_model_used
            );

            if (!modelToUse) {
              modelToUse = appDefaultModel as Model;
            }

            return {
              model: modelToUse,
              parentMessageNode: {
                ...conversation.parentMessageNode,
              } as MessageNode,
              conversationId: conversation.id,
              conversationState: conversation.state as ConversationState,
              groupConversationId: conversation.group_conversation_id,
              id: conversation.id,
            };
          });

        setChatSessions(sessionsData);
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        log.error({ err, skipSentry: true }, 'Error fetching conversation history');
        setError(err);
      } finally {
        setIsLoading(false);
      }
    },
    [providers, setChatSessions, appDefaultModel]
  );

  // Setup conversation on component mount or conversationId change
  useEffect(() => {
    if (
      groupConversationId &&
      !fetchedRef.current.has(groupConversationId) &&
      providers.length > 0
    ) {
      fetchConversationHistory(groupConversationId, providers);
      fetchedRef.current.add(groupConversationId);
    }
  }, [groupConversationId, fetchConversationHistory, providers]);

  useEffect(() => {
    if (!groupConversationId && chatSessions.length === 0) {
      fetchedRef.current.clear();
      initializeNewChat();
    }
  }, [groupConversationId, initializeNewChat, chatSessions]);

  useEffect(() => {
    if (loadedConversations.length > 0 && groupConversationId) {
      const conversation = loadedConversations.find(
        (c) => c.id === groupConversationId
      );
      if (conversation) {
        setSelectedConversation(conversation);
      }
    }
  }, [loadedConversations, groupConversationId, setSelectedConversation]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      fetchedRef.current.clear();
    };
  }, []);

  return { fetchConversationHistory, isLoading, error };
}
