import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useTour } from '@/hooks/useTour';
import { PlayCircle } from 'lucide-react';

export function TourSettings() {
  const { startTour, tourCompleted } = useTour();

  return (
    <Card>
      <CardHeader>
        <CardTitle>Onboarding Tour</CardTitle>
        <CardDescription>
          Take a guided tour of Sabi Chat's features and capabilities.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-sm font-medium">
              {tourCompleted ? 'Tour completed' : 'Tour not completed'}
            </p>
            <p className="text-xs text-muted-foreground">
              Learn about workspaces, prompts, model comparison, and more.
            </p>
          </div>
          <Button onClick={startTour} variant="outline" size="sm">
            <PlayCircle className="w-4 h-4 mr-2" />
            {tourCompleted ? 'Restart Tour' : 'Start Tour'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
