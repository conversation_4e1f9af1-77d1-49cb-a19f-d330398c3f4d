import React from 'react';
import { StepType } from '@reactour/tour';
import { FeatureAccess } from '@/hooks/useFeatureAccess';

export interface TourStep extends StepType {
  id: string;
  requiresFeature?: keyof FeatureAccess;
  subscriptionRequired?: string[];
}

export const createTourSteps = (featureAccess: FeatureAccess): TourStep[] => {
  const baseSteps: TourStep[] = [
    {
      id: 'welcome',
      selector: '[data-tour="chat-area"]',
      content: (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Welcome to Sabi Chat! 🎉</h3>
          <p>Let's take a quick tour to help you get started with our AI-powered chat platform.</p>
        </div>
      ),
    },
    {
      id: 'workspaces',
      selector: '[data-tour="workspace-selector"]',
      content: (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Workspaces</h3>
          <p>Organize your conversations by creating different workspaces for projects, topics, or teams.</p>
          <p className="text-sm text-muted-foreground">Click here to create or switch between workspaces.</p>
        </div>
      ),
    },
    {
      id: 'model-selection',
      selector: '[data-tour="model-selector"]',
      content: (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Choose Your AI Model</h3>
          <p>Select from different AI models based on your subscription plan.</p>
          <p className="text-sm text-muted-foreground">
            {featureAccess.canUseComparison ? 'You have access to premium models!' : 'Upgrade for access to more powerful models.'}
          </p>
        </div>
      ),
    },
    {
      id: 'prompt-library-sidebar',
      selector: '[data-tour="prompt-library-sidebar"]',
      content: (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Prompt Library</h3>
          <p>Save and reuse your best prompts. Access your library from the sidebar.</p>
          {!featureAccess.canUsePromptLibrary && (
            <p className="text-sm text-amber-600">Upgrade to Starter plan to create custom prompts.</p>
          )}
        </div>
      ),
    },
    {
      id: 'chat-management',
      selector: '[data-tour="conversation-list"]',
      content: (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Manage Conversations</h3>
          <p>Organize your chats with bulk actions: archive, delete, or favorite multiple conversations at once.</p>
          <p className="text-sm text-muted-foreground">Right-click or use the selection menu for bulk operations.</p>
        </div>
      ),
    },
  ];

  // Conditional steps based on subscription
  if (featureAccess.canUsePromptLibrary) {
    baseSteps.push({
      id: 'prompt-enhancement',
      selector: '[data-tour="enhance-prompt-button"]',
      content: (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Enhance Your Prompts ✨</h3>
          <p>Use AI to improve your prompts for better results. Available in the chat input area.</p>
          <p className="text-sm text-muted-foreground">Click the enhance button next to your message.</p>
        </div>
      ),
      requiresFeature: 'canUsePromptLibrary',
    });

    baseSteps.push({
      id: 'prompt-library-input',
      selector: '[data-tour="prompt-library-input"]',
      content: (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Quick Prompt Access</h3>
          <p>Access your prompt library directly from the chat input using the prompt button.</p>
        </div>
      ),
    });
  }

  if (featureAccess.canUseComparison) {
    baseSteps.push({
      id: 'model-comparison',
      selector: '[data-tour="comparison-button"]',
      content: (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Compare Models 🔄</h3>
          <p>Get responses from multiple AI models side-by-side to find the best answer.</p>
          <p className="text-sm text-muted-foreground">Perfect for important decisions or creative tasks.</p>
        </div>
      ),
      requiresFeature: 'canUseComparison',
      subscriptionRequired: ['starter', 'premium'],
    });
  }

  baseSteps.push({
    id: 'completion',
    selector: '[data-tour="chat-input"]',
    content: (
      <div className="space-y-3">
        <h3 className="text-lg font-semibold">You're All Set! 🚀</h3>
        <p>Start chatting with AI and explore all the features we've shown you.</p>
        <p className="text-sm text-muted-foreground">You can restart this tour anytime from Settings.</p>
      </div>
    ),
  });

  return baseSteps;
};
