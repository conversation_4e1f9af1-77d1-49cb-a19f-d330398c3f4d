'use client';
import { useState, useEffect, useCallback, useContext } from 'react';
import { useTour as useReactTour } from '@reactour/tour';
import { useFeatureAccess } from '@/hooks/useFeatureAccess';
import { createTourSteps } from '@/config/tourSteps';
import { createClient } from '@/utils/supabase/client';
import { UserContext } from '@/providers/AuthProvider';
import { logger } from '@/lib/logger';

const TOUR_VERSION = 1;

export function useTour() {
  const { user } = useContext(UserContext);
  const featureAccess = useFeatureAccess();
  const { setIsOpen, setSteps, setCurrentStep } = useReactTour();
  const [tourCompleted, setTourCompleted] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check if user has completed tour
  const checkTourStatus = useCallback(async () => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('user_preferences')
        .select('tour_completed, tour_version')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        logger.error('Error checking tour status:', error);
        setTourCompleted(false);
      } else if (!data) {
        // No preferences record exists, user hasn't completed tour
        setTourCompleted(false);
      } else {
        // Check if tour was completed and version is current
        const completed = data.tour_completed && (data.tour_version || 0) >= TOUR_VERSION;
        setTourCompleted(completed);
      }
    } catch (error) {
      logger.error('Error checking tour status:', error);
      setTourCompleted(false);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Mark tour as completed
  const completeTour = useCallback(async () => {
    if (!user) return;

    try {
      const supabase = createClient();
      const { error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          tour_completed: true,
          tour_completed_at: new Date().toISOString(),
          tour_version: TOUR_VERSION,
        });

      if (error) {
        logger.error('Error marking tour as completed:', error);
      } else {
        setTourCompleted(true);
      }
    } catch (error) {
      logger.error('Error completing tour:', error);
    }
  }, [user]);

  // Start tour
  const startTour = useCallback(() => {
    const steps = createTourSteps(featureAccess);
    setSteps(steps);
    setCurrentStep(0);
    setIsOpen(true);
  }, [featureAccess, setSteps, setCurrentStep, setIsOpen]);

  // Auto-start tour for new users
  useEffect(() => {
    if (!isLoading && tourCompleted === false && user) {
      // Small delay to ensure UI is ready
      const timer = setTimeout(() => {
        startTour();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [isLoading, tourCompleted, user, startTour]);

  useEffect(() => {
    checkTourStatus();
  }, [checkTourStatus]);

  return {
    tourCompleted,
    isLoading,
    startTour,
    completeTour,
  };
}
