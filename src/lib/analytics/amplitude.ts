import { AnalyticsProvider, UserTraits, EventProperties } from './provider';
import * as amplitude from '@amplitude/analytics-browser';

export class AmplitudeAnalyticsProvider implements AnalyticsProvider {
  private analytics: typeof amplitude | null = null;

  init() {
    // Only initialize analytics in production environment
    if (process.env.NODE_ENV !== 'production') {
      console.log('Analytics disabled in development environment');
      return;
    }

    // Read the key at runtime to allow for proper testing
    const AMPLITUDE_WRITE_KEY = process.env.NEXT_PUBLIC_AMPLITUDE_WRITE_KEY;
    if (!AMPLITUDE_WRITE_KEY) {
      console.warn('Amplitude Write Key not found. Analytics disabled.');
      return;
    }

    if (!this.analytics) {
      try {
        // Initialize Amplitude SDK
        this.analytics = amplitude;
        this.analytics.init(AMPLITUDE_WRITE_KEY, {
          autocapture: {
            networkTracking: true,
          },
        });
      } catch (error) {
        console.error('Failed to initialize Amplitude analytics:', error);
        this.analytics = null;
      }
    }
  }

  identify(userId: string, traits: UserTraits) {
    // Only track in production
    if (process.env.NODE_ENV !== 'production' || !this.analytics) return;

    try {
      // Set the user ID
      this.analytics.setUserId(userId);

      // Create identify event and set user properties
      const identifyEvent = new amplitude.Identify();

      // Limit the number of traits to prevent performance issues
      const traitEntries = Object.entries(traits).slice(0, 50);

      // Set user traits as properties
      traitEntries.forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          try {
            // Convert value to valid types for Amplitude
            let amplitudeValue: string | number | boolean;

            if (
              typeof value === 'string' ||
              typeof value === 'number' ||
              typeof value === 'boolean'
            ) {
              amplitudeValue = value;
            } else if (value instanceof Date) {
              amplitudeValue = value.toISOString();
            } else {
              amplitudeValue = String(value);
            }

            identifyEvent.set(key, amplitudeValue);
          } catch (error) {
            console.warn(`Failed to set trait ${key}:`, error);
          }
        }
      });

      // Send the identify event
      this.analytics.identify(identifyEvent);
    } catch (error) {
      console.error('Failed to identify user in Amplitude:', error);
    }
  }

  track(eventName: string, properties?: EventProperties) {
    // Only track in production
    if (process.env.NODE_ENV !== 'production' || !this.analytics) return;

    try {
      this.analytics.track(eventName, properties);
    } catch (error) {
      console.error(`Failed to track event ${eventName} in Amplitude:`, error);
    }
  }

  reset() {
    // Only reset in production
    if (process.env.NODE_ENV !== 'production' || !this.analytics) return;

    try {
      this.analytics.reset();
    } catch (error) {
      console.error('Failed to reset Amplitude analytics:', error);
    }
  }
}
