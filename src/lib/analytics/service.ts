import { AnalyticsProvider, UserTraits, EventProperties } from './provider';
import { AmplitudeAnalyticsProvider } from './amplitude';

// Instantiate the desired provider
// Note: Analytics only runs in production environment for privacy and performance
const provider: AnalyticsProvider = new AmplitudeAnalyticsProvider();

export const AnalyticsService = {
  init: () => {
    provider.init();
  },

  identify: (userId: string, traits: UserTraits) => {
    provider.identify(userId, traits);
  },

  track: (eventName: string, properties?: EventProperties) => {
    // Optionally add global properties here if needed (e.g., app version)
    provider.track(eventName, properties);
  },

  reset: () => {
    provider.reset();
  },
};

// Define standard event names
export enum AnalyticsEvent {
  SIGNED_IN = 'Signed In',
  MESSAGE_SENT = 'Message Sent',
  CONVERSATION_RENAMED = 'Conversation Renamed',
  CONVERSATION_FAVORITED = 'Conversation Favorited',
  CONVERSATION_ARCHIVED = 'Conversation Archived',
  CONVERSATION_DELETED = 'Conversation Deleted',
  SHARE_LINK_CREATED = 'Share Link Created',
  MODEL_CHANGED = 'Model Changed',
  MESSAGE_RETRIED = 'Message Retried',
  MESSAGE_EDITED = 'Message Edited',
  SEARCH_USED = 'Search Used',
  SETTINGS_CHANGED = 'Settings Changed',
}
