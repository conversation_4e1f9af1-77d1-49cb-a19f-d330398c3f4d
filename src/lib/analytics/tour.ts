export const trackTourEvent = (event: string, properties?: Record<string, any>) => {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', event, {
      event_category: 'onboarding_tour',
      ...properties,
    });
  }
};

export const tourAnalytics = {
  started: () => trackTourEvent('tour_started'),
  completed: () => trackTourEvent('tour_completed'),
  skipped: (step: number) => trackTourEvent('tour_skipped', { step }),
  stepViewed: (stepId: string, stepNumber: number) => 
    trackTourEvent('tour_step_viewed', { step_id: stepId, step_number: stepNumber }),
};
