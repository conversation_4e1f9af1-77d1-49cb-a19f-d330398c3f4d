import { logger } from '@/lib/logger';
import * as Sen<PERSON> from '@sentry/nextjs';
import type { Logger } from 'pino';

type ErrorMetadata = {
  timestamp?: string;
  [key: string]: string | number | boolean | undefined;
};

/**
 * Reports errors to Sentry in production environment
 * In development, only logs to console to avoid noise
 */
function reportError(
  error: Error,
  metadata: {
    tags?: Record<string, string>;
    extra?: Record<string, unknown>;
    context?: string;
  } = {}
) {
  // Only report to external services in production
  if (process.env.NODE_ENV === 'production') {
    // Report to Sentry
    try {
      Sentry.captureException(error, {
        tags: metadata.tags,
        extra: metadata.extra,
        contexts: metadata.context ? { custom: { context: metadata.context } } : undefined,
      });
    } catch (sentryError) {
      console.error('Failed to report to Sentry:', sentryError);
    }


  }
}

// Base error class for all application errors
export class AppError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public cause?: Error,
    public metadata: ErrorMetadata = {}
  ) {
    super(message, { cause });
    this.name = this.constructor.name;

    // Log error when created
    logger.error(
      {
        err: this.toJSON(),
        metadata,
        stack: this.stack,
      },
      `${this.name} occurred`
    );

    // Report to error monitoring services
    reportError(this, {
      tags: {
        errorCode: this.code,
        statusCode: this.statusCode.toString(),
        errorType: this.name,
      },
      extra: {
        ...metadata,
        cause: this.cause?.message,
        stack: this.stack,
      },
      context: `${this.name}: ${this.code}`,
    });
  }

  static from(
    error: Error,
    code: string = 'INTERNAL_ERROR',
    statusCode: number = 500
  ) {
    return new AppError(error.message, code, statusCode, error);
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      cause: this.cause?.message,
      metadata: this.metadata,
    };
  }
}

// AI/LLM related errors
export class AiError extends AppError {
  constructor(
    message: string,
    code: ErrorCode = ErrorCode.AI_COMPLETION,
    metadata: ErrorMetadata = {}
  ) {
    super(message, code, 500, undefined, metadata);
  }

  static streamingError(message: string, cause?: Error) {
    return new AiError(message, ErrorCode.AI_STREAMING, {
      streamingFailed: true,
      timestamp: new Date().toISOString(),
      cause: cause?.message,
    });
  }

  static contentBlocked(message: string, cause?: Error) {
    return new AiError(message, ErrorCode.AI_CONTENT_BLOCKED, {
      contentBlocked: true,
      timestamp: new Date().toISOString(),
      cause: cause?.message,
    });
  }

  static rateLimitError(message: string, retryAfter?: number) {
    return new AiError(message, ErrorCode.AI_RATE_LIMIT, {
      retryAfter,
      timestamp: new Date().toISOString(),
    });
  }
}

// Database related errors
export class DatabaseError extends AppError {
  constructor(message: string, code: string = 'DATABASE_ERROR', cause?: Error) {
    super(message, code, 500, cause);
  }

  static from(error: Error) {
    return new DatabaseError(error.message, 'DATABASE_ERROR', error);
  }
}

// API related errors
export class ApiError extends AppError {
  constructor(
    message: string,
    code: string = 'API_ERROR',
    statusCode: number = 500,
    cause?: Error
  ) {
    super(message, code, statusCode, cause);
  }

  static override from(
    error: Error,
    code: string = 'API_ERROR',
    statusCode: number = 500
  ) {
    return new ApiError(error.message, code, statusCode, error);
  }
}

// Authentication related errors
export class AuthError extends AppError {
  constructor(message: string, code: string = 'AUTH_ERROR', cause?: Error) {
    super(message, code, 401, cause);
  }

  static from(error: Error) {
    return new AuthError(error.message, 'AUTH_ERROR', error);
  }
}

// Validation related errors
export class ValidationError extends AppError {
  constructor(
    message: string,
    code: string = 'VALIDATION_ERROR',
    cause?: Error
  ) {
    super(message, code, 400, cause);
  }

  static from(error: Error) {
    return new ValidationError(error.message, 'VALIDATION_ERROR', error);
  }
}

// Rate limiting errors
export class RateLimitError extends AppError {
  constructor(
    message: string,
    code: string = 'RATE_LIMIT_ERROR',
    cause?: Error
  ) {
    super(message, code, 429, cause);
  }

  static from(error: Error) {
    return new RateLimitError(error.message, 'RATE_LIMIT_ERROR', error);
  }
}

// Network related errors
export class NetworkError extends AppError {
  constructor(message: string, code: string = 'NETWORK_ERROR', cause?: Error) {
    super(message, code, 503, cause);
  }

  static from(error: Error) {
    return new NetworkError(error.message, 'NETWORK_ERROR', error);
  }
}

// Error type guard
export function isAppError(error: unknown): error is AppError {
  return error instanceof AppError;
}

// Error handler utility
export function handleError(error: unknown): AppError {
  if (isAppError(error)) {
    return error;
  }

  if (error instanceof Error) {
    // Report non-AppError instances directly before wrapping
    reportError(error, {
      tags: {
        errorType: 'UnhandledError',
        originalType: error.constructor.name,
      },
      extra: {
        originalMessage: error.message,
        stack: error.stack,
      },
      context: 'handleError: Non-AppError instance',
    });
    return AppError.from(error);
  }

  // For unknown errors
  const appError = new AppError(
    typeof error === 'string' ? error : 'An unknown error occurred',
    'UNKNOWN_ERROR'
  );

  // Already captured in constructor
  return appError;
}

export enum ErrorCode {
  // Generic errors
  UNKNOWN = 'UNKNOWN_ERROR',
  INTERNAL = 'INTERNAL_ERROR',

  // AI/LLM errors
  AI_COMPLETION = 'AI_COMPLETION_ERROR',
  AI_STREAMING = 'AI_STREAMING_ERROR',
  AI_RATE_LIMIT = 'AI_RATE_LIMIT_ERROR',
  AI_CONTENT_BLOCKED = 'AI_CONTENT_BLOCKED',
  // Database errors
  DATABASE_ERROR = 'DATABASE_ERROR',

  // API errors
  API_VALIDATION = 'API_VALIDATION_ERROR',
  API_UNAUTHORIZED = 'API_UNAUTHORIZED',
  API_RESOURCE_EXHAUSTED = 'API_RESOURCE_EXHAUSTED',
  API_NOT_FOUND = 'API_NOT_FOUND',

  // Auth errors
  AUTH_INVALID_CREDENTIALS = 'AUTH_INVALID_CREDENTIALS',
  AUTH_TOKEN_EXPIRED = 'AUTH_TOKEN_EXPIRED',
  AUTH_INSUFFICIENT_PERMISSIONS = 'AUTH_INSUFFICIENT_PERMISSIONS',

  // Invalid request errors
  INVALID_REQUEST = 'INVALID_REQUEST',

  // Quota errors
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
}

// ============================================================================
// Enhanced Error Reporting Utilities
// ============================================================================

/**
 * Reports an error directly to Sentry without throwing an exception.
 * Use this when you want to report an error but continue execution.
 *
 * @param error - The error to report
 * @param context - Optional context string to help identify where the error occurred
 * @param metadata - Optional metadata to include with the error report
 *
 * @example
 * ```typescript
 * try {
 *   await riskyOperation();
 * } catch (error) {
 *   // Report to Sentry but don't throw - continue with fallback
 *   reportErrorToSentry(error, 'Failed to sync user data', { userId });
 *   return fallbackData;
 * }
 * ```
 */
export function reportErrorToSentry(
  error: Error,
  context?: string,
  metadata?: Record<string, unknown>
): void {
  reportError(error, {
    tags: {
      source: 'manual_report',
      reportedVia: 'reportErrorToSentry',
    },
    extra: {
      reportedAt: new Date().toISOString(),
      ...metadata,
    },
    context: context || 'Manual error report',
  });
}

/**
 * Logs an error and ensures it gets reported to Sentry.
 * This bypasses the logger's filtering and always reports the error.
 *
 * @param error - The error to log and report
 * @param message - Log message describing what happened
 * @param loggerInstance - Logger instance to use (defaults to the main logger)
 * @param metadata - Optional metadata to include
 *
 * @example
 * ```typescript
 * try {
 *   await criticalOperation();
 * } catch (error) {
 *   // Always log and report critical errors
 *   logAndReportError(
 *     error,
 *     'Critical operation failed - immediate attention required',
 *     logger,
 *     { operationId, userId }
 *   );
 * }
 * ```
 */
export function logAndReportError(
  error: Error,
  message: string,
  loggerInstance: Logger = logger,
  metadata?: Record<string, unknown>
): void {
  // Log the error first. We include a `skipSentry` flag so that the enhanced
  // logger does not forward this particular log entry to Sentry. This prevents
  // duplicate reporting when we fall back to a direct `reportError` call.
  loggerInstance.error({ err: error, skipSentry: true, ...metadata }, message);

  // Manually report the error to Sentry. Because we set `skipSentry` above,
  // this will be the only Sentry event generated for this error.
  reportError(error, {
    tags: {
      source: 'logAndReport',
      logLevel: 'error',
    },
    extra: {
      logMessage: message,
      reportedAt: new Date().toISOString(),
      ...metadata,
    },
    context: `logAndReportError: ${message}`,
  });
}

/**
 * Reports a warning-level error to Sentry.
 * Use this for non-critical errors that should be tracked but don't require immediate attention.
 *
 * @param error - The error to report
 * @param context - Optional context string
 * @param metadata - Optional metadata to include
 *
 * @example
 * ```typescript
 * try {
 *   await optionalFeature();
 * } catch (error) {
 *   // Report as warning - feature failed but app continues normally
 *   reportWarningToSentry(error, 'Optional feature failed', { featureName });
 * }
 * ```
 */
export function reportWarningToSentry(
  error: Error,
  context?: string,
  metadata?: Record<string, unknown>
): void {
  try {
    // Delegate to the shared reporting helper so that all global Sentry
    // configuration (enabled flag, sample rate, etc.) is respected.
    reportError(error, {
      tags: {
        source: 'manual_warning',
        reportedVia: 'reportWarningToSentry',
        severity: 'warning',
      },
      extra: {
        reportedAt: new Date().toISOString(),
        ...metadata,
      },
      context: context ?? 'Manual warning report',
    });
  } catch (captureError) {
    // Use console.error directly to avoid the enhanced logger and prevent
    // potential infinite recursion if Sentry reporting itself fails.
    console.error('Failed to report warning to Sentry:', captureError);
  }
}

/**
 * Enhanced error handler that provides better error context and reporting.
 * This replaces the basic handleError function for critical code paths.
 *
 * @param error - The error to handle
 * @param context - Context about where the error occurred
 * @param metadata - Additional metadata to include
 * @returns AppError instance
 *
 * @example
 * ```typescript
 * try {
 *   await databaseOperation();
 * } catch (error) {
 *   throw handleErrorWithContext(
 *     error,
 *     'Database operation in user service',
 *     { userId, operation: 'updateProfile' }
 *   );
 * }
 * ```
 */
export function handleErrorWithContext(
  error: unknown,
  context: string,
  metadata?: Record<string, unknown>
): AppError {
  if (isAppError(error)) {
    // If it's already an AppError, create a *shallow clone* with merged metadata
    // to avoid mutating the original instance which might be re-used elsewhere.
    const mergedMetadata = {
      ...error.metadata,
      additionalContext: context,
      ...metadata,
    } as typeof error.metadata;

    // Clone the existing error without invoking the constructor again to prevent
    // duplicate logging / Sentry reporting. We copy all enumerable properties
    // from the original instance and then override the metadata field.
    const clonedError: AppError = Object.assign(
      Object.create(Object.getPrototypeOf(error)),
      error,
      { metadata: mergedMetadata },
    );

    return clonedError;
  }

  if (error instanceof Error) {
    // Wrap the original Error once; the AppError constructor will handle reporting
    return new AppError(
      error.message,
      'ENHANCED_HANDLED_ERROR',
      500,
      error,
      { context, ...metadata }
    );
  }

  // For unknown errors, create a comprehensive AppError
  const appError = new AppError(
    typeof error === 'string' ? error : 'An unknown error occurred',
    'UNKNOWN_ERROR_WITH_CONTEXT',
    500,
    undefined,
    {
      context,
      originalError: typeof error === 'object' ? JSON.stringify(error) : String(error),
      ...metadata
    }
  );

  return appError;
}

/**
 * Create an error boundary helper that reports errors to Sentry.
 * Useful for wrapping critical async operations.
 *
 * @param operation - The async operation to wrap
 * @param context - Context for error reporting
 * @param metadata - Additional metadata
 * @returns Result of the operation or throws enhanced error
 *
 * @example
 * ```typescript
 * const result = await withErrorBoundary(
 *   () => criticalAsyncOperation(userId),
 *   'Critical user operation',
 *   { userId, operation: 'processPayment' }
 * );
 * ```
 */
export async function withErrorBoundary<T>(
  operation: () => Promise<T>,
  context: string,
  metadata?: Record<string, unknown>
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    throw handleErrorWithContext(error, context, metadata);
  }
}
