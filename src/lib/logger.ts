import pino, { Logger } from 'pino';
import * as Sentry from '@sentry/nextjs';

// Utility to parse and validate the sample rate (0.0 to 1.0).
// Falls back to 1.0 if the env var is missing or invalid.
const getSampleRate = (): number => {
  const rawValue = process.env.SENTRY_ERROR_SAMPLE_RATE;
  const parsed = rawValue !== undefined ? Number(rawValue) : 1.0;

  // If the value is not a finite number within 0-1, use the default.
  if (!Number.isFinite(parsed) || parsed < 0 || parsed > 1) {
    // Use console.warn directly to avoid potential recursion with the logger
    if (process.env.NODE_ENV !== 'production') {
      console.warn(
        `Invalid SENTRY_ERROR_SAMPLE_RATE value "${rawValue}". Falling back to the default sample rate (1.0).`
      );
    }
    return 1.0;
  }

  return parsed;
};

// Configuration for Sentry error reporting
const SENTRY_CONFIG = {
  // Sample rate for error reporting (0.0 to 1.0)
  sampleRate: getSampleRate(),
  // Patterns to skip reporting (comma-separated)
  skipPatterns:
    process.env.SENTRY_SKIP_PATTERNS?.split(',').map((p) => p.trim()) || [],
  // Enable/disable automatic Sentry reporting
  enabled:
    process.env.SENTRY_AUTO_REPORT !== 'false' &&
    process.env.NODE_ENV === 'production',
  // Explicit control for warning reporting (disabled by default)
  reportWarnings: process.env.SENTRY_REPORT_WARNINGS === 'true',
} as const;

// Error severity classification for better Sentry organization
const ERROR_SEVERITY = {
  CRITICAL: [
    'AI_STREAMING_ERROR',
    'DATABASE_ERROR',
    'INTERNAL_ERROR',
    'NETWORK_ERROR',
  ],
  HIGH: ['AI_RATE_LIMIT_ERROR', 'API_ERROR', 'AUTH_ERROR', 'QUOTA_EXCEEDED'],
  MEDIUM: [
    'VALIDATION_ERROR',
    'API_VALIDATION_ERROR',
    'AUTH_INVALID_CREDENTIALS',
  ],
  LOW: ['UNKNOWN_ERROR', 'API_NOT_FOUND'],
} as const;

// Determine if an error should be reported to Sentry
function shouldReportToSentry(
  error: Error,
  logMessage?: string,
  logContext?: Record<string, unknown>
): boolean {
  if (!SENTRY_CONFIG.enabled) return false;

  // Sample rate check
  if (Math.random() > SENTRY_CONFIG.sampleRate) return false;

  const errorMessage = error.message?.toLowerCase() || '';
  const contextString = JSON.stringify(logContext || {}).toLowerCase();
  const fullMessage = `${errorMessage} ${logMessage || ''} ${contextString}`;

  // Skip based on configured patterns
  for (const pattern of SENTRY_CONFIG.skipPatterns) {
    if (pattern && fullMessage.includes(pattern.toLowerCase())) {
      return false;
    }
  }

  // Skip common non-critical errors that create noise
  const skipPatterns = [
    'connection timeout',
    'client disconnected',
    'stream closed',
    'request cancelled',
    'network timeout',
    'cors',
    'request aborted',
  ];

  for (const pattern of skipPatterns) {
    if (fullMessage.includes(pattern)) {
      return false;
    }
  }

  // Skip rate limit errors if they're happening frequently (they're tracked in metrics)
  if (
    fullMessage.includes('rate limit') ||
    fullMessage.includes('rate_limit')
  ) {
    return Math.random() < 0.1; // Only report 10% of rate limit errors
  }

  return true;
}

// Get error severity level for Sentry
function getErrorSeverity(error: Error): Sentry.SeverityLevel {
  const errorCode = (error as Error & { code?: string })?.code;

  if (!errorCode) return 'error';

  if ((ERROR_SEVERITY.CRITICAL as readonly string[]).includes(errorCode))
    return 'fatal';
  if ((ERROR_SEVERITY.HIGH as readonly string[]).includes(errorCode))
    return 'error';
  if ((ERROR_SEVERITY.MEDIUM as readonly string[]).includes(errorCode))
    return 'warning';
  if ((ERROR_SEVERITY.LOW as readonly string[]).includes(errorCode))
    return 'info';

  return 'error';
}

// Safe error reporting to Sentry with proper error handling
function reportErrorToSentry(
  error: Error,
  metadata: {
    tags?: Record<string, string>;
    extra?: Record<string, unknown>;
    context?: string;
    level?: Sentry.SeverityLevel;
  } = {}
): void {
  if (!SENTRY_CONFIG.enabled) return;

  try {
    const severity = metadata.level || getErrorSeverity(error);

    Sentry.captureException(error, {
      level: severity,
      tags: {
        source: 'enhanced_logger',
        errorType: error.constructor.name,
        ...metadata.tags,
      },
      extra: {
        timestamp: new Date().toISOString(),
        ...metadata.extra,
      },
      contexts: metadata.context
        ? {
            custom: {
              context: metadata.context,
              loggerSource: true,
            },
          }
        : undefined,
    });
  } catch (sentryError) {
    // Use console.error directly to avoid infinite loops
    console.error('Failed to report error to Sentry:', sentryError);
  }
}

const getLogLevel = (): string => {
  const levels =
    process.env.LOGGING_LEVELS?.split(',').map((l) => l.trim().toLowerCase()) ||
    [];

  if (levels.includes('none')) return 'silent';
  if (levels.includes('all')) return 'trace';
  if (levels.includes('error')) return 'error';
  if (levels.includes('warn')) return 'warn';
  if (levels.includes('info')) return 'info';
  if (levels.includes('debug')) return 'debug';

  return 'warn'; // Default log level
};

// Custom error serializer that preserves all error properties
const errorSerializer = (err: unknown) => {
  if (!err || typeof err !== 'object') return err;

  const errorObj = err as Record<string, unknown>;
  const serialized: Record<string, unknown> = {
    type: errorObj.constructor?.name || 'Unknown',
    message: (err as unknown as Error).message,
    stack: (err as unknown as Error).stack,
  };

  // Copy all enumerable properties (including Stripe-specific ones)
  Object.keys(errorObj).forEach((key) => {
    serialized[key] = errorObj[key];
  });

  return serialized;
};

// Enhanced logger wrapper that automatically reports errors to Sentry
function createEnhancedLogger(baseLogger: Logger): Logger {
  // Helper function to handle error reporting
  const handleErrorReporting = (
    error: Error,
    msg?: string,
    obj?: Record<string, unknown>,
    level: 'error' | 'warning' = 'error'
  ) => {
    if (shouldReportToSentry(error, msg, obj)) {
      process.nextTick(() => {
        reportErrorToSentry(error, {
          level,
          tags: {
            logLevel: level === 'error' ? 'error' : 'warn',
            source: level === 'error' ? 'logger_error' : 'logger_warn',
          },
          extra: {
            logMessage: msg,
            logContext: obj
              ? { ...obj, err: undefined, error: undefined }
              : undefined,
          },
          context: msg || `Logger ${level} method`,
        });
      });
    }
  };

  // Create a wrapper that preserves all original logger properties but enhances error/warn
  return new Proxy(baseLogger, {
    get(target, prop) {
      // Utility to extract error, message, and context from Pino logger arguments
      const extractLogData = (
        logArgs: unknown[]
      ): { error?: Error; msg?: string; context?: Record<string, unknown> } => {
        if (!logArgs.length) return {};

        const [first, second] = logArgs;
        let error: Error | undefined;
        let msg: string | undefined;
        let context: Record<string, unknown> | undefined;

        // Case 1: logger.error(Error, "msg", obj?)
        if (first instanceof Error) {
          error = first;
          if (typeof second === 'string') msg = second;
          if (
            typeof second === 'object' &&
            second !== null &&
            !(second instanceof Error)
          ) {
            context = second as Record<string, unknown>;
          } else if (
            logArgs.length > 2 &&
            typeof logArgs[2] === 'object' &&
            logArgs[2] !== null &&
            !(logArgs[2] instanceof Error)
          ) {
            context = logArgs[2] as Record<string, unknown>;
          }
        } else if (typeof first === 'object' && first !== null) {
          // Case 2: logger.error({ err: Error | string | unknown, ... }, "msg")
          context = first as Record<string, unknown>;
          const maybeErr =
            (first as Record<string, unknown>).err ||
            (first as Record<string, unknown>).error;
          if (maybeErr instanceof Error) {
            error = maybeErr;
          } else if (typeof maybeErr === 'string') {
            error = new Error(maybeErr);
          }
          if (typeof second === 'string') msg = second;
        } else if (typeof first === 'string') {
          // Case 3: logger.error("msg", Error | obj?)
          msg = first;
          if (second instanceof Error) {
            error = second;
          } else if (typeof second === 'object' && second !== null) {
            context = second as Record<string, unknown>;
          }
        }

        return { error, msg, context };
      };

      if (prop === 'error') {
        return (...args: unknown[]) => {
          // Always log first using the original method
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const result = (target as any).error(...args);

          const { error, msg, context } = extractLogData(args);
          if (
            error &&
            !(context && (context as Record<string, unknown>).skipSentry)
          ) {
            handleErrorReporting(error, msg, context, 'error');
          }

          return result;
        };
      }

      if (prop === 'warn') {
        return (...args: unknown[]) => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const result = (target as any).warn(...args);

          if (SENTRY_CONFIG.reportWarnings) {
            const { error, msg, context } = extractLogData(args);
            if (
              error &&
              !(context && (context as Record<string, unknown>).skipSentry)
            ) {
              handleErrorReporting(error, msg, context, 'warning');
            }
          }

          return result;
        };
      }

      if (prop === 'child') {
        return (
          bindings: Record<string, unknown>,
          options?: Record<string, unknown>
        ) => {
          const childLogger = target.child(bindings, options);
          return createEnhancedLogger(childLogger);
        };
      }

      // For all other properties, return the original
      return target[prop as keyof Logger];
    },
  });
}

// Create the base logger
const baseLogger: Logger =
  process.env.NODE_ENV === 'production'
    ? pino({
        level: getLogLevel(),
        serializers: {
          err: errorSerializer,
          error: errorSerializer, // Support both 'err' and 'error' fields
        },
      })
    : pino({
        transport: {
          target: 'pino-pretty',
          options: { colorize: true },
        },
        level: getLogLevel(),
        serializers: {
          err: errorSerializer,
          error: errorSerializer, // Support both 'err' and 'error' fields
        },
      });

// Export the enhanced logger
export const logger: Logger = createEnhancedLogger(baseLogger);
