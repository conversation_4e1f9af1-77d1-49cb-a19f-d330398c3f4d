import { RateLimiterRedis } from 'rate-limiter-flexible';
import { redis } from '@/lib/redis';
import { NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

const log = logger.child({ module: 'rate-limit' });

// IP-based rate limiter for general API endpoints
export const rateLimiter = new RateLimiterRedis({
  storeClient: redis,
  points: 10, // 10 requests
  duration: 60, // per 60 seconds
});

// User-based rate limiter for authenticated endpoints
export const userRateLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'user_rl',
  points: 10, // 10 requests
  duration: 60, // per 60 seconds
});

/**
 * Helper function to check user rate limit and return appropriate response
 * (RateLimiterRes from rate-limiter-flexible provides msBeforeNext when the limit is exceeded).
 */

export async function checkUserRateLimit(
  userId: string
): Promise<NextResponse | null> {
  try {
    await userRateLimiter.consume(userId);
    return null; // No rate limit hit
  } catch (err) {
    const msBeforeNext = (err as { msBeforeNext?: number }).msBeforeNext;
    if (typeof msBeforeNext === 'number') {
      const retryAfter = Math.ceil(msBeforeNext / 1000);
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        {
          status: 429,
          headers: { 'Retry-After': retryAfter.toString() },
        }
      );
    }

    // Unexpected error (e.g. Redis connectivity issue). Treat as 503.
    log.error({ err }, 'User rate limiter internal error');
    return NextResponse.json(
      { error: 'Service unavailable' },
      { status: 503 }
    );
  }
}

type RouteHandler<R extends Request = Request, Rest extends unknown[] = []> = (
  req: R,
  ...rest: Rest
) => Response | Promise<Response>;

function hasIp(request: Request): request is Request & { ip: string } {
  return (
    'ip' in request && typeof (request as { ip?: unknown }).ip === 'string'
  );
}

export function withRateLimit<R extends Request, Rest extends unknown[] = []>(
  handler: RouteHandler<R, Rest>
) {
  return async (req: R, ...rest: Rest): Promise<Response> => {
    // Determine client IP in order of precedence:
    // 1. "req.ip" when available (populated by Next.js / underlying adapter)
    // 2. First address in the "X-Forwarded-For" header (may contain multiple)
    // 3. "X-Real-IP" header
    const forwardedFor = req.headers.get('x-forwarded-for');
    const clientIp =
      (hasIp(req) ? req.ip : undefined) ??
      (forwardedFor ? forwardedFor.split(',')[0].trim() : undefined) ??
      req.headers.get('x-real-ip') ??
      undefined;

    if (!clientIp) {
      // Missing IP information indicates a malformed request (e.g. misconfigured proxy)
      return new Response('Unable to determine client IP', { status: 400 });
    }

    try {
      await rateLimiter.consume(clientIp);
    } catch (err) {
      // Distinguish between a genuine rate-limit exceed event and an internal failure
      const msBeforeNext = (err as { msBeforeNext?: number }).msBeforeNext;
      if (typeof msBeforeNext === 'number') {
        const retryAfter = Math.ceil(msBeforeNext / 1000);
        return new Response('Rate limit exceeded', {
          status: 429,
          headers: { 'Retry-After': retryAfter.toString() },
        });
      }

      // For unexpected errors (e.g. Redis connectivity issues) respond with 503
      // and avoid leaking internal details to the client.
      log.error({ err }, 'Rate limiter internal error');
      return new Response('Service unavailable', { status: 503 });
    }

    return handler(req, ...rest);
  };
}
