import Redis from 'ioredis';
import { logger } from '@/lib/logger';

const log = logger.child({ module: 'redis' });

export const redis = new Redis(process.env.REDIS_URL! + '?family=0');

redis.on('connect', () => {
  log.info('Redis connected');
});

redis.on('reconnecting', () => {
  log.info('Redis reconnecting');
});

redis.on('ready', () => {
  log.info('Redis ready');
});

redis.on('error', (err) => {
  log.error({ err }, 'Redis error');
});