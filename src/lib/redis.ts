import Redis from 'ioredis';
import { logger } from '@/lib/logger';

const log = logger.child({ module: 'redis' });

function normalizeRedisUrl(raw: string): string {
  const value = raw && raw.trim().length > 0 ? raw.trim() : 'localhost:6379';
  const hasScheme = /^(redis|rediss):\/\//i.test(value);
  const withScheme = hasScheme ? value : `redis://${value}`;
  try {
    const url = new URL(withScheme);
    // Ensure the family param is present; default to 0 to avoid IPv6-only attempts
    if (!url.searchParams.has('family')) {
      url.searchParams.set('family', '0');
    }
    return url.toString();
  } catch {
    // Fallback: if URL constructor fails for any reason, append query manually
    return hasScheme ? `${withScheme}${withScheme.includes('?') ? '&' : '?'}family=0` : `redis://${value}?family=0`;
  }
}

const normalizedRedisUrl = normalizeRedisUrl(process.env.REDIS_URL || 'localhost:6379');

export const redis = new Redis(normalizedRedisUrl, { family: 0 });

redis.on('connect', () => {
  log.info('Redis connected');
});

redis.on('reconnecting', () => {
  log.info('Redis reconnecting');
});

redis.on('ready', () => {
  log.info('Redis ready');
});

redis.on('error', (err) => {
  log.error({ err }, 'Redis error');
});