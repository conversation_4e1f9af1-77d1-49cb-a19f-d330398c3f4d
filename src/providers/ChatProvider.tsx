'use client';
import React, {
  useState,
  createContext,
  useCallback,
  useEffect,
  useContext,
  useRef,
} from 'react';
import {
  GroupConversation,
  ConversationState,
  Provider,
  MessageNode,
  UrlCitationAnnotation,
  Model,
  StreamMetadataClientData,
} from '@/lib/supabase/types';
import { logger } from '@/lib/logger';
import { v4 as uuidv4 } from 'uuid';
import { UserContext } from '@/providers/AuthProvider';
import { useSubscription } from './SubscriptionProvider';
import {
  APP_DEFAULT_MODEL,
  PROCESSING_MODELS_STORAGE_KEY,
} from '@/constants/chat';
import {
  createModelSelectionService,
  ModelSelectionResult,
  ModelSelectionService,
} from '@/services/modelSelection';

const log = logger.child({
  module: 'ChatProvider',
});

export interface ChatSession {
  id: string;
  model: Model;
  conversationId: string;
  // Id of the parent group conversation this session belongs to
  groupConversationId?: string;
  conversationState: ConversationState;
  parentMessageNode: MessageNode | null;
}

// Interface for storing partial message content
interface PartialMessage {
  content: string;
  annotations: UrlCitationAnnotation[];
}

// Interface for SSE stream subscription details
interface StreamSubscription {
  eventSource: EventSource;
  onDelta: (text: string, replace?: boolean, attachments?: unknown[]) => void;
  onAnnotation: (ann: UrlCitationAnnotation) => void;
  onError: (err: Error) => void;
  onDone: () => void;
  onMetadata: (metadata: StreamMetadataClientData) => void;
  groupConversationId?: string; // Add groupConversationId to track which group a conversation belongs to
}

// Abort controller for unified streams
interface UnifiedStreamController {
  abort: () => void;
  groupConversationId: string;
}

interface ChatContextType {
  providers: Provider[];
  setProviders: (providers: Provider[]) => void;
  chatSessions: ChatSession[];
  setChatSessions: React.Dispatch<React.SetStateAction<ChatSession[]>>;
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
  selectedConversation: GroupConversation | null;
  isChatLoading: boolean;
  isConversationListLoading: boolean;
  isBackgroundRefreshing: boolean;
  hasInitiallyLoaded: boolean;
  setIsChatLoading: (isChatLoading: boolean) => void;
  loadedConversations: GroupConversation[];
  fetchUserConversations: (isBackgroundRefresh?: boolean) => Promise<void>;
  fetchMoreConversations: () => Promise<void>;
  hasMoreConversations: boolean;
  isFetchingMoreConversations: boolean;
  setSelectedConversation: (conversation: GroupConversation | null) => void;
  initializeNewChat: (
    workspaceId?: string,
    promptDefaultModel?: string
  ) => Promise<void>;
  setUserConversations: React.Dispatch<
    React.SetStateAction<GroupConversation[]>
  >;
  performSearch: (query: string) => Promise<GroupConversation[]>;
  fetchRecentConversations: () => Promise<GroupConversation[]>;
  subscribeToStream: (
    assistantMessageId: string,
    conversationId: string,
    model: string,
    isTestMode: boolean,
    useWebSearch: boolean,
    useImageGeneration: boolean,
    onDelta: (text: string, replace?: boolean, attachments?: unknown[]) => void,
    onAnnotation: (ann: UrlCitationAnnotation) => void,
    onError: (err: Error) => void,
    onDone: () => void,
    onMetadata: (metadata: StreamMetadataClientData) => void,
    workspaceId?: string
  ) => void;
  unsubscribeFromStream: (assistantMessageId: string) => void;
  partialMessages: Map<string, PartialMessage>;
  getPartialMessage: (messageId: string) => PartialMessage | undefined;
  clearPartialMessage: (messageId: string) => void;
  sseConnections: Map<string, StreamSubscription>; // Add sseConnections to expose streaming state
  appDefaultModel: Model | null;
  lastModelSelection: ModelSelectionResult | null;
  getModelSelectionReason: () => string | null;
  updateSessionModelId: (modelId: string) => void;
  registerStream: (
    sessionId: string,
    controller: UnifiedStreamController
  ) => void;
  abortStream: (sessionId: string) => void;
  streamingSessionIds: Set<string>;
}

export const ChatContext = createContext<ChatContextType>({
  providers: [],
  setProviders: () => {},
  chatSessions: [],
  setChatSessions: () => {},
  isSidebarOpen: false,
  toggleSidebar: () => {},
  selectedConversation: null,
  isChatLoading: true,
  isConversationListLoading: false,
  isBackgroundRefreshing: false,
  hasInitiallyLoaded: false,
  setIsChatLoading: () => {},
  loadedConversations: [],
  fetchUserConversations: async () => {},
  fetchMoreConversations: async () => {},
  hasMoreConversations: false,
  isFetchingMoreConversations: false,
  setSelectedConversation: () => {},
  initializeNewChat: async () => {},
  setUserConversations: () => {},
  performSearch: async () => [],
  fetchRecentConversations: async () => [],
  subscribeToStream: () => {},
  unsubscribeFromStream: () => {},
  partialMessages: new Map(),
  getPartialMessage: () => undefined,
  clearPartialMessage: () => {},
  sseConnections: new Map(), // Add default value for sseConnections
  appDefaultModel: null,
  lastModelSelection: null,
  getModelSelectionReason: () => null,
  updateSessionModelId: () => {},
  registerStream: () => {},
  abortStream: () => {},
  streamingSessionIds: new Set(),
});

export function ChatProvider({ children }: { children: React.ReactNode }) {
  const [providers, setProviders] = React.useState<Provider[]>([]);
  const [chatSessions, setChatSessions] = React.useState<ChatSession[]>([]);
  const [isSidebarOpen, setIsSidebarOpen] = React.useState(true);
  const [selectedConversation, setSelectedConversation] =
    useState<GroupConversation | null>(null);
  const [isChatLoading, setIsChatLoading] = useState(true);
  const [isConversationListLoading, setIsConversationListLoading] =
    useState(false);
  const [isBackgroundRefreshing, setIsBackgroundRefreshing] = useState(false);
  const [hasInitiallyLoaded, setHasInitiallyLoaded] = useState(false);
  const [loadedConversations, setUserConversations] = useState<
    GroupConversation[]
  >([]);
  const [nextConversationCursor, setNextConversationCursor] = useState<
    string | null
  >(null);
  const [hasMoreConversations, setHasMoreConversations] = useState(false);
  const [isFetchingMoreConversations, setIsFetchingMoreConversations] =
    useState(false);
  const [appDefaultModel, setAppDefaultModel] = useState<Model | null>(null);
  // Store active SSE connections for message streams
  const [sseConnections, setSseConnections] = useState<
    Map<string, StreamSubscription>
  >(new Map());
  // Use ref to track current connections for cleanup (avoids stale closure issues)
  const sseConnectionsRef = useRef<Map<string, StreamSubscription>>(new Map());
  const unifiedStreamControllersRef = useRef<
    Map<string, UnifiedStreamController>
  >(new Map());
  const [streamingSessionIds, setStreamingSessionIds] = useState(
    new Set<string>()
  );
  // Store partial message content keyed by message ID
  const [partialMessages, setPartialMessages] = useState<
    Map<string, PartialMessage>
  >(new Map());
  // Store model selection result for showing reasons to users
  const [lastModelSelection, setLastModelSelection] =
    useState<ModelSelectionResult | null>(null);

  // Keep ref in sync with state for reliable cleanup
  useEffect(() => {
    sseConnectionsRef.current = sseConnections;
  }, [sseConnections]);

  const { me, user } = useContext(UserContext);
  const { accessibleModelIds } = useSubscription();

  const registerStream = useCallback(
    (sessionId: string, controller: UnifiedStreamController) => {
      unifiedStreamControllersRef.current.set(sessionId, controller);
      setStreamingSessionIds((prev) => new Set(prev).add(sessionId));
    },
    []
  );

  const abortStream = useCallback((sessionId: string) => {
    const controller = unifiedStreamControllersRef.current.get(sessionId);
    if (controller) {
      try {
        controller.abort();
      } catch (e) {
        log.warn({ err: e, sessionId }, 'Error aborting stream');
      }
      unifiedStreamControllersRef.current.delete(sessionId);
      setStreamingSessionIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(sessionId);
        return newSet;
      });
    }
  }, []);

  // Helper functions for partial messages
  const getPartialMessage = useCallback(
    (messageId: string) => partialMessages.get(messageId),
    [partialMessages]
  );

  const clearPartialMessage = useCallback((messageId: string) => {
    setPartialMessages((prev) => {
      const newMap = new Map(prev);
      newMap.delete(messageId);
      return newMap;
    });
  }, []);

  // Cleanup stale or closed SSE connections
  const cleanupStaleConnections = useCallback(() => {
    setSseConnections((prevConnections) => {
      const activeConnections = new Map();

      prevConnections.forEach((connection, messageId) => {
        const readyState = connection.eventSource.readyState;
        // Keep connection if it's connecting (0) or open (1)
        if (
          readyState === EventSource.CONNECTING ||
          readyState === EventSource.OPEN
        ) {
          activeConnections.set(messageId, connection);
        } else {
          // Connection is closed (2), clean it up
          try {
            connection.eventSource.close();
          } catch (error) {
            console.warn('Error closing stale SSE connection:', error);
          }
        }
      });

      return activeConnections.size !== prevConnections.size
        ? activeConnections
        : prevConnections;
    });
  }, []);

  // Periodic cleanup of stale connections
  useEffect(() => {
    const interval = setInterval(() => {
      cleanupStaleConnections();
    }, 30000); // Clean up every 30 seconds

    return () => clearInterval(interval);
  }, [cleanupStaleConnections]);

  const getModelSelectionReason = useCallback(() => {
    if (!lastModelSelection) return null;
    return ModelSelectionService.formatSelectionReason(lastModelSelection);
  }, [lastModelSelection]);

  const updatePartialMessage = useCallback(
    (messageId: string, content: string) => {
      setPartialMessages((prev) => {
        const newMap = new Map(prev);
        const existing = newMap.get(messageId) || {
          content: '',
          annotations: [],
        };
        newMap.set(messageId, {
          ...existing,
          content: existing.content + content,
        });
        return newMap;
      });
    },
    []
  );

  const updatePartialMessageAnnotation = useCallback(
    (messageId: string, annotation: UrlCitationAnnotation) => {
      setPartialMessages((prev) => {
        const newMap = new Map(prev);
        const existing = newMap.get(messageId) || {
          content: '',
          annotations: [],
        };
        newMap.set(messageId, {
          ...existing,
          annotations: [...existing.annotations, annotation],
        });
        return newMap;
      });
    },
    []
  );

  const toggleSidebar = useCallback(() => {
    setIsSidebarOpen((prev) => !prev);
  }, []);

  const fetchUserConversations = useCallback(
    async (isBackgroundRefresh = false) => {
      // Only show full loading spinner on initial load
      if (!isBackgroundRefresh) {
        setIsConversationListLoading(true);
      } else {
        setIsBackgroundRefreshing(true);
      }

      try {
        const response = await fetch('/api/groupConversations');
        const { data, nextCursor, error } = await response.json();
        if (error) throw new Error(error);
        setUserConversations(data);
        setNextConversationCursor(nextCursor);
        setHasMoreConversations(!!nextCursor);

        // Mark as initially loaded after first successful fetch
        if (!hasInitiallyLoaded) {
          setHasInitiallyLoaded(true);
        }
      } catch (error) {
        log.error({ err: error, skipSentry: true }, 'Error fetching conversations');
        setUserConversations([]);
        setNextConversationCursor(null);
        setHasMoreConversations(false);
      } finally {
        // Only clear the loading state that was set for this specific fetch
        if (!isBackgroundRefresh) {
          setIsConversationListLoading(false);
        } else {
          setIsBackgroundRefreshing(false);
        }
      }
    },
    [hasInitiallyLoaded]
  );

  const fetchMoreConversations = useCallback(async () => {
    if (!hasMoreConversations || isFetchingMoreConversations) {
      return;
    }

    setIsFetchingMoreConversations(true);
    try {
      const encodedCursor = nextConversationCursor
        ? encodeURIComponent(nextConversationCursor)
        : '';
      const response = await fetch(
        `/api/groupConversations?cursor=${encodedCursor}`
      );
      const { data, nextCursor, error } = await response.json();

      if (error) throw new Error(error);

      setUserConversations((prev) => {
        const existingIds = new Set(prev.map((c: GroupConversation) => c.id));
        const newConversations = data.filter(
          (c: GroupConversation) => !existingIds.has(c.id)
        );
        return [...prev, ...newConversations];
      });
      setNextConversationCursor(nextCursor);
      setHasMoreConversations(!!nextCursor);
    } catch (error) {
      log.error({ err: error, skipSentry: true }, 'Error fetching more conversations');
    } finally {
      setIsFetchingMoreConversations(false);
    }
  }, [
    hasMoreConversations,
    isFetchingMoreConversations,
    nextConversationCursor,
  ]);

  const findModelAndProvider = useCallback(
    (modelId: string) => {
      for (const provider of providers) {
        const model = provider.models.find((m) => m.id === modelId);
        if (model) {
          return model;
        }
      }
    },
    [providers]
  );

  const updateSessionModelId = useCallback(
    (modelId: string) => {
      if (
        providers.length === 0 ||
        !user?.id ||
        accessibleModelIds.length === 0
      )
        return;

      const modelSelectionService = createModelSelectionService({
        accessibleModelIds,
        userDefaultModelId: me?.preferences?.default_model_id || undefined,
        workspaceDefaultModel: undefined,
        promptDefaultModel: modelId,
      });

      const selectionResult = modelSelectionService.selectModel();

      const modelToUse = findModelAndProvider(selectionResult.modelId);

      if (!modelToUse) {
        log.error('Could not find model object for selected model ID', {
          modelId,
          userId: user.id,
        });
        return;
      }

      setChatSessions((prev) => {
        const newSessions = [...prev];
        newSessions[0].model = modelToUse;
        return newSessions;
      });
    },
    [providers, user, accessibleModelIds, me, findModelAndProvider]
  );

  const initializeNewChat = useCallback(
    async (workspaceId?: string, promptDefaultModel?: string) => {
      if (
        providers.length === 0 ||
        !user?.id ||
        accessibleModelIds.length === 0
      )
        return;

      try {
        // Get workspace default model if in workspace context
        let workspaceDefaultModel: string | undefined;
        if (workspaceId) {
          try {
            const response = await fetch(`/api/workspaces/${workspaceId}`);
            if (response.ok) {
              const { workspace } = await response.json();
              workspaceDefaultModel = workspace?.default_model_id;
            }
          } catch (error) {
            log.error(
              { error, workspaceId },
              'Error fetching workspace default model'
            );
          }
        }

        // Create model selection service
        const modelSelectionService = createModelSelectionService({
          accessibleModelIds,
          userDefaultModelId: me?.preferences?.default_model_id || undefined,
          workspaceDefaultModel,
          promptDefaultModel,
        });

        // Select the best model
        const selectionResult = modelSelectionService.selectModel();

        // Store the selection result for potential user feedback
        setLastModelSelection(selectionResult);

        // Find the actual model object
        const modelToUse = findModelAndProvider(selectionResult.modelId);

        if (!modelToUse) {
          log.error('Could not find model object for selected model ID', {
            modelId: selectionResult.modelId,
            userId: user.id,
          });
          return;
        }

        // Update last used model if this was system selection (not explicit user choice)
        if (selectionResult.reason === 'explicit_selection') {
          ModelSelectionService.updateLastUsedModel(selectionResult.modelId);
        }

        setChatSessions([
          {
            model: modelToUse,
            conversationId: '',
            parentMessageNode: null,
            conversationState: 'healthy',
            id: uuidv4(),
            groupConversationId: undefined,
          },
        ]);
        setSelectedConversation(null);
      } catch (error) {
        log.error({ err: error, userId: user.id, skipSentry: true }, 'Error in model selection');
      }
    },
    [
      providers,
      setChatSessions,
      setSelectedConversation,
      accessibleModelIds,
      user,
      me,
      findModelAndProvider,
    ]
  );

  const fetchInitialData = useCallback(async () => {
    setIsChatLoading(true);
    try {
      // Fetch providers and models
      const providersResponse = await fetch('/api/providers');

      if (!providersResponse.ok) throw new Error('Failed to fetch providers');
      const providersData: Provider[] = await providersResponse.json();
      setProviders(providersData);
      const [provider, model] = APP_DEFAULT_MODEL.split('/');
      const modelProvider = providersData.find((p) => p.name === provider);
      if (modelProvider) {
        const modelToUse = modelProvider.models.find((m) => m.name === model);
        if (modelToUse) {
          setAppDefaultModel(modelToUse);
        }
      }
    } catch (error) {
      log.error({ err: error, skipSentry: true }, 'Error fetching initial data');
      // Handle error state appropriately, maybe clear providers/models?
      setProviders([]);
    } finally {
      setIsChatLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]);

  // cleanup processing models from session storage. Check for storage processing models and remove any that are not in the current chatSessions array
  useEffect(() => {
    const storageProcessingModels = JSON.parse(
      sessionStorage.getItem(PROCESSING_MODELS_STORAGE_KEY) || '[]'
    );

    const currentModelIds = chatSessions.map((model) => model.id);

    // Keep only the models that are currently active
    const updatedProcessingModels = storageProcessingModels.filter(
      (modelId: string) => currentModelIds.includes(modelId)
    );

    // If the list has changed, update sessionStorage
    if (updatedProcessingModels.length !== storageProcessingModels.length) {
      sessionStorage.setItem(
        PROCESSING_MODELS_STORAGE_KEY,
        JSON.stringify(updatedProcessingModels)
      );
    }
  }, [chatSessions]);

  const performSearch = useCallback(
    async (query: string): Promise<GroupConversation[]> => {
      if (!query.trim()) return [];

      try {
        const response = await fetch(
          `/api/search/conversations?query=${encodeURIComponent(query)}`
        );
        if (!response.ok) throw new Error('Search failed');

        const { results } = await response.json();
        return results;
      } catch (error) {
        log.error({ err: error, skipSentry: true }, 'Error searching conversations');
        return [];
      }
    },
    []
  );

  const fetchRecentConversations = useCallback(async (): Promise<
    GroupConversation[]
  > => {
    return loadedConversations.slice(0, 10);
  }, [loadedConversations]);

  // Subscribe to an SSE stream for a message
  const subscribeToStream = useCallback(
    (
      assistantMessageId: string,
      conversationId: string,
      model: string,
      isTestMode: boolean,
      useWebSearch: boolean,
      useImageGeneration: boolean,
      onDelta: (
        text: string,
        replace?: boolean,
        attachments?: unknown[]
      ) => void,
      onAnnotation: (ann: UrlCitationAnnotation) => void,
      onError: (err: Error) => void,
      onDone: () => void,
      onMetadata: (metadata: StreamMetadataClientData) => void,
      workspaceId?: string
    ) => {
      // If already subscribed, reconnect with new callbacks
      if (sseConnections.has(assistantMessageId)) {
        const existingConnection = sseConnections.get(assistantMessageId)!;

        // Update callbacks but keep the connection
        setSseConnections(
          new Map(sseConnections).set(assistantMessageId, {
            ...existingConnection,
            onDelta,
            onAnnotation,
            onError,
            onDone,
            onMetadata,
          })
        );
        return;
      }

      // Build query params for the SSE endpoint
      const params = new URLSearchParams({
        assistantMessageId,
        conversationId,
        model,
        isTestMode: isTestMode.toString(),
        useWebSearch: useWebSearch.toString(),
        useImageGeneration: useImageGeneration.toString(),
      });

      if (workspaceId) {
        params.set('workspaceId', workspaceId);
      }

      // Create a new EventSource connection
      const es = new EventSource(`/api/chat/stream-sse/events?${params}`);

      // Set up event listeners
      es.addEventListener('delta', (event) => {
        try {
          const data = JSON.parse((event as MessageEvent).data);
          if (data.type === 'delta' && data.content !== undefined) {
            // Handle replace flag for image generation
            if (data.replace) {
              // For replace deltas, reset the partial message instead of appending
              setPartialMessages((prev) => {
                const newMap = new Map(prev);
                newMap.set(assistantMessageId, {
                  content: data.content,
                  annotations: prev.get(assistantMessageId)?.annotations || [],
                });
                return newMap;
              });
            } else {
              // Normal delta - append content
              updatePartialMessage(assistantMessageId, data.content);
            }

            // Pass to component callback with replace flag and attachments
            onDelta(data.content, data.replace, data.attachments);
          }
        } catch (error) {
          log.error(
            'Error parsing delta event:',
            error,
            (event as MessageEvent).data
          );
        }
      });

      es.addEventListener('annotation', (event) => {
        try {
          const data = JSON.parse((event as MessageEvent).data);
          if (data.type === 'annotation' && data.annotation) {
            // Store annotation in provider state AND pass to component callback
            updatePartialMessageAnnotation(assistantMessageId, data.annotation);
            onAnnotation(data.annotation);
          }
        } catch (error) {
          log.error(
            'Error parsing annotation event:',
            error,
            (event as MessageEvent).data
          );
        }
      });

      es.addEventListener('metadata', (event) => {
        const data = JSON.parse((event as MessageEvent).data);
        if (data.type === 'metadata' && data.metadata) {
          onMetadata(data.metadata);
        }
      });

      es.addEventListener('error', (event) => {
        // Parse error details coming from the SSE stream
        interface StreamErrorPayload {
          type?: string;
          error?: {
            message?: string;
          };
        }

        let errorMessage = 'Unknown error in EventSource';
        let parsedData: unknown = undefined;
        try {
          parsedData = JSON.parse((event as MessageEvent).data || '{}');
          const payload = parsedData as StreamErrorPayload;
          if (payload.type === 'error' && payload.error?.message) {
            errorMessage = payload.error.message;
          }
        } catch {
          // Swallow JSON parse errors – we'll still log the raw event below
        }

        // Structured console + pino logger output
        log.error(
          {
            errorMessage,
            eventType: event.type,
            rawEventData: (event as MessageEvent).data,
            parsedData,
          },
          'EventSource stream error'
        );

        // Report to Sentry only in production to avoid noise locally
        if (process.env.NODE_ENV === 'production') {
          import('@sentry/nextjs')
            .then((Sentry) => {
              Sentry.captureException(new Error(errorMessage), {
                tags: {
                  component: 'EventSource',
                  phase: 'stream-error',
                },
                extra: {
                  parsedData,
                  rawEventData: (event as MessageEvent).data,
                },
              });
            })
            .catch((sentryError) => {
              console.error(
                'Failed to report EventSource error to Sentry:',
                sentryError
              );
            });
        }

        onError(new Error(errorMessage));
      });

      es.addEventListener('done', () => {
        onDone();
        es.close();

        // remove this connection from state
        setSseConnections((prev) => {
          const newMap = new Map(prev);
          newMap.delete(assistantMessageId);
          return newMap;
        });

        // don't clear the partial message immediately,
        // but clean it up after a short delay
        setTimeout(() => {
          clearPartialMessage(assistantMessageId);
        }, 3000);
      });

      // Set up general error handler
      es.onerror = (event) => {
        log.error({ eventType: event.type }, 'General EventSource error');

        if (process.env.NODE_ENV === 'production') {
          import('@sentry/nextjs')
            .then((Sentry) => {
              Sentry.captureException(new Error('General EventSource error'), {
                tags: {
                  component: 'EventSource',
                  phase: 'onerror',
                },
                extra: {
                  eventType: event.type,
                },
              });
            })
            .catch((sentryError) => {
              console.error(
                'Failed to report general EventSource error to Sentry:',
                sentryError
              );
            });
        }
        // The browser will automatically try to reconnect
      };

      // Store the connection
      setSseConnections(
        new Map(sseConnections).set(assistantMessageId, {
          eventSource: es,
          onDelta,
          onAnnotation,
          onError,
          onDone,
          onMetadata,
          groupConversationId: selectedConversation?.id,
        })
      );
    },
    [
      sseConnections,
      updatePartialMessage,
      updatePartialMessageAnnotation,
      clearPartialMessage,
      selectedConversation,
    ]
  );

  // Unsubscribe from an SSE stream
  const unsubscribeFromStream = useCallback(
    (assistantMessageId: string) => {
      const connection = sseConnections.get(assistantMessageId);
      if (connection) {
        connection.eventSource.close();
        setSseConnections((prev) => {
          const newMap = new Map(prev);
          newMap.delete(assistantMessageId);
          return newMap;
        });
      }
    },
    [sseConnections]
  );

  // Clean up all open SSE connections when the provider unmounts
  useEffect(() => {
    return () => {
      // Use ref to access current connections (avoids stale closure)
      sseConnectionsRef.current.forEach((connection: StreamSubscription) => {
        try {
          connection.eventSource.close();
        } catch (error) {
          console.warn('Error closing SSE connection:', error);
        }
      });
      // Clear the connections
      sseConnectionsRef.current.clear();
      unifiedStreamControllersRef.current.forEach((controller) => {
        try {
          controller.abort();
        } catch (e) {
          console.warn('Error aborting unified stream on unmount', e);
        }
      });
      unifiedStreamControllersRef.current.clear();
    };
  }, []); // Empty dependency array is correct here since we use ref

  return (
    <ChatContext.Provider
      value={{
        providers,
        setProviders,
        chatSessions,
        setChatSessions,
        isSidebarOpen,
        toggleSidebar,
        selectedConversation,
        isChatLoading,
        isConversationListLoading,
        isBackgroundRefreshing,
        hasInitiallyLoaded,
        setIsChatLoading,
        loadedConversations,
        fetchUserConversations,
        fetchMoreConversations,
        hasMoreConversations,
        isFetchingMoreConversations,
        setSelectedConversation,
        initializeNewChat,
        setUserConversations,
        performSearch,
        fetchRecentConversations,
        subscribeToStream,
        unsubscribeFromStream,
        partialMessages,
        getPartialMessage,
        clearPartialMessage,
        sseConnections,
        appDefaultModel,
        lastModelSelection,
        getModelSelectionReason,
        updateSessionModelId,
        registerStream,
        abortStream,
        streamingSessionIds,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
}
