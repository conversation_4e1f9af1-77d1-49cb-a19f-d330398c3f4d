'use client';

import {
  SubscriptionData,
  SubscriptionPlan,
  ModelTier,
  PLAN_MODEL_ACCESS,
  SUBSCRIPTION_PLANS,
  DailyUsage,
  LLMModel,
} from '@/lib/supabase/types';
import { UserContext } from '@/providers/AuthProvider';
import { createClient } from '@/utils/supabase/client';
import {
  createContext,
  useState,
  useEffect,
  useContext,
  ReactNode,
  useCallback,
  useMemo,
} from 'react';
import { logger } from '@/lib/logger';

const log = logger.child({
  module: 'SubscriptionProvider',
});

interface QuotaStatus {
  tokens?: {
    used: number;
    remaining: number;
    total: number;
    resetDate: string;
  };
  images?: {
    used: number;
    remaining: number;
    total: number;
    resetDate: string;
  };
  comparisons?: {
    used: number;
    remaining: number;
    total: number;
    resetDate: string;
  };
}

interface SubscriptionContextValue {
  isLoading: boolean;
  subscription: SubscriptionData | null;
  canSendMessage: boolean;
  canAccessModelTier: (tier: ModelTier) => boolean;
  canAccessPremiumModels: () => boolean; // Kept for compatibility if needed
  messagesRemaining: number;
  getPlan: () => SubscriptionPlan;
  refreshSubscription: () => void;
  handleMessageSent: () => void;
  accessibleModelIds: string[];
  quotaStatus: QuotaStatus | null;
  canUseComparison: boolean;
  refreshQuota: () => void;
  workspaceCount: number;
  canCreateWorkspace: boolean;
  canUsePromptLibrary: boolean;
  canUsePromptEnhancement: boolean;
}

type DailyUsageRealtimePayload = {
  new: DailyUsage;
  old: DailyUsage;
  eventType: 'UPDATE';
};

const SubscriptionContext = createContext<SubscriptionContextValue | undefined>(
  undefined
);
const supabase = createClient();
export function SubscriptionProvider({ children }: { children: ReactNode }) {
  const [subscription, setSubscription] = useState<SubscriptionData | null>(
    null
  );
  const [accessibleModelIds, setAccessibleModelIds] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [dailyMessageCount, setDailyMessageCount] = useState<number>(0);
  const [quotaStatus, setQuotaStatus] = useState<QuotaStatus | null>(null);
  const [workspaceCount, setWorkspaceCount] = useState<number>(0);
  const { isAuthenticated, user } = useContext(UserContext); // Get user from AuthProvider

  const fetchDailyUsage = useCallback(async () => {
    const usageResponse = await fetch('/api/usage/daily');
    if (!usageResponse.ok) {
      throw new Error(`API error: ${usageResponse.statusText}`);
    }
    const usageResult = await usageResponse.json();
    const { data: usageData, error: usageError } = usageResult;
    if (usageError)
      throw new Error(
        typeof usageError === 'string' ? usageError : JSON.stringify(usageError)
      );
    return usageData?.count || 0;
  }, []);

  const fetchQuotaStatus = useCallback(async () => {
    if (!isAuthenticated) {
      setQuotaStatus(null);
      return;
    }

    try {
      const quotaResponse = await fetch('/api/me/quota');
      if (!quotaResponse.ok) {
        throw new Error(`Quota API error: ${quotaResponse.statusText}`);
      }
      const quotaData = await quotaResponse.json();
      setQuotaStatus(quotaData);
    } catch (error) {
      log.error({ err: error, skipSentry: true }, 'Failed to fetch quota status');
      setQuotaStatus(null);
    }
  }, [isAuthenticated]);

  const fetchWorkspaceCount = useCallback(async () => {
    if (!isAuthenticated) {
      setWorkspaceCount(0);
      return;
    }

    try {
      const workspacesResponse = await fetch('/api/workspaces');
      if (!workspacesResponse.ok) {
        throw new Error(
          `Workspaces API error: ${workspacesResponse.statusText}`
        );
      }
      const workspacesData = await workspacesResponse.json();
      setWorkspaceCount(workspacesData.workspaces?.length || 0);
    } catch (error) {
      log.error({ err: error, skipSentry: true }, 'Failed to fetch workspace count');
      setWorkspaceCount(0);
    }
  }, [isAuthenticated]);
  const fetchSubscriptionData = useCallback(async () => {
    // Only fetch if user is authenticated
    if (!isAuthenticated) {
      setSubscription(null);
      setDailyMessageCount(0);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setSubscription(null); // Reset on refetch
      setDailyMessageCount(0); // Reset on refetch

      // Fetch subscription details
      const [subscriptionResponse, accessibleModelsResponse] =
        await Promise.all([
          fetch('/api/subscriptions'),
          fetch('/api/me/accessible-models'),
        ]);
      if (!subscriptionResponse.ok) {
        throw new Error(
          `Subscription API error: ${subscriptionResponse.statusText}`
        );
      }
      if (!accessibleModelsResponse.ok) {
        throw new Error(
          `Accessible‑models API error: ${accessibleModelsResponse.statusText}`
        );
      }
      const subscriptionResult = await subscriptionResponse.json();
      const accessibleModelsResult = await accessibleModelsResponse.json();
      const { data: subscriptionData, error: subscriptionError } =
        subscriptionResult;
      const accessibleModelsData = accessibleModelsResult.map(
        (model: LLMModel) => model.id
      );

      setAccessibleModelIds(accessibleModelsData);

      if (subscriptionError) {
        // Handle errors returned in the JSON body
        throw new Error(
          typeof subscriptionError === 'string'
            ? subscriptionError
            : JSON.stringify(subscriptionError)
        );
      }

      if (subscriptionData) {
        setSubscription(subscriptionData);

        // Only fetch daily message count for free users or if no active subscription
        if (
          subscriptionData.plan === 'free' ||
          !['active', 'trialing'].includes(subscriptionData.status)
        ) {
          const usageCount = await fetchDailyUsage();

          if (usageCount) {
            setDailyMessageCount(usageCount || 0);
          }
        }
      } else {
        // No data returned, assume free plan equivalent
        setSubscription(null);
        // Fetch daily message count if no subscription data exists
        const usageCount = await fetchDailyUsage();
        if (usageCount) setDailyMessageCount(usageCount || 0);
      }

      // Fetch quota status and workspace count for all authenticated users
      await Promise.all([fetchQuotaStatus(), fetchWorkspaceCount()]);
    } catch (error) {
      log.error('Error fetching subscription data:', error);
      // Default to free plan representation if there's an error
      setSubscription(null);
      // Attempt to fetch daily usage even on error, might still be relevant
      try {
        const usageCount = await fetchDailyUsage();
        if (usageCount) setDailyMessageCount(usageCount || 0);
      } catch (usageError) {
        log.error(
          'Error fetching daily usage after subscription error:',
          usageError
        );
        setDailyMessageCount(0); // Reset if usage fetch fails too
      }
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated]); // Rerun when authentication status changes

  useEffect(() => {
    fetchSubscriptionData();
  }, [fetchSubscriptionData]); // Fetch data on mount and when user/auth state changes

  // use effect to listen for changes in the subscriptions table
  useEffect(() => {
    if (!user?.id) {
      return;
    }

    // Create a new channel each time to ensure we have the current date
    const todayDate = new Date().toISOString().split('T')[0];

    const channel = supabase
      .channel(`subscription_changes_${todayDate}`)
      .on(
        'postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'subscriptions' },
        () => fetchSubscriptionData()
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_daily_usage',
          filter: `date=eq.${todayDate}`,
        },
        (payload) => {
          const dailyUsagePayload =
            payload as unknown as DailyUsageRealtimePayload;
          setDailyMessageCount(dailyUsagePayload.new.message_count);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user?.id, fetchSubscriptionData]);

  // Effect to handle daily reset by monitoring date changes
  useEffect(() => {
    if (!isAuthenticated) return;

    const checkForDateChange = () => {
      const now = new Date();
      const currentDate = now.toISOString().split('T')[0];
      const lastCheckDate = localStorage.getItem('lastQuotaCheckDate');

      if (lastCheckDate !== currentDate) {
        localStorage.setItem('lastQuotaCheckDate', currentDate);
        // Reset daily message count and refetch subscription data
        setDailyMessageCount(0);
        fetchSubscriptionData();
      }
    };

    // Check immediately
    checkForDateChange();

    // Set up interval to check every minute
    const interval = setInterval(checkForDateChange, 60000);

    return () => clearInterval(interval);
  }, [isAuthenticated, fetchSubscriptionData]);

  const canSendMessage = useMemo(() => {
    if (isLoading) return true; // Allow while loading to prevent UI flashing

    const currentPlan = subscription?.plan || 'free';
    const planDetails = SUBSCRIPTION_PLANS[currentPlan];

    // Check if subscription is active or if it's the free plan
    const isActive = subscription
      ? ['active', 'trialing'].includes(subscription.status)
      : false;

    // For free plan, check daily message limit
    if (currentPlan === 'free') {
      // Check if the subscription status is something other than active/trialing (e.g., past_due, canceled)
      // Treat these cases like the free plan limit applies if there was ever a subscription record.
      // If no subscription record exists at all (subscription is null), also apply free limit.
      if (subscription && !isActive) {
        return dailyMessageCount < planDetails.maxDailyMessages;
      } else if (!subscription) {
        return dailyMessageCount < planDetails.maxDailyMessages;
      }
      // If subscription exists and is free plan (implicitly active as it wouldn't have a status otherwise from API?)
      // Or handle free explicitly if API returns a free sub with status
      return dailyMessageCount < planDetails.maxDailyMessages;
    }

    // For paid plans, simply check if subscription is active
    return isActive;
  }, [subscription, dailyMessageCount, isLoading]);

  const canAccessModelTier = (tier: ModelTier): boolean => {
    if (isLoading) return false; // Don't allow access while loading

    const currentPlan = subscription?.plan || 'free';
    const isActive = subscription
      ? ['active', 'trialing'].includes(subscription.status)
      : false;

    // If not active and not free plan, only allow free tier
    if (!isActive && currentPlan !== 'free') {
      return tier === 'free';
    }

    // If free plan (regardless of subscription object existence), only allow free tier
    if (currentPlan === 'free') {
      return tier === 'free';
    }

    // For active paid plans, check if the tier is allowed for the plan
    const allowedTiers = PLAN_MODEL_ACCESS[currentPlan];
    return allowedTiers.includes(tier);
  };

  const canAccessPremiumModels = (): boolean => {
    return canAccessModelTier('premium');
  };

  const getMessagesRemaining = useCallback((): number => {
    const currentPlan = subscription?.plan || 'free';
    const planDetails = SUBSCRIPTION_PLANS[currentPlan];
    const isActive = subscription
      ? ['active', 'trialing'].includes(subscription.status)
      : false;

    if (currentPlan === 'free' || (subscription && !isActive)) {
      return Math.max(0, planDetails.maxDailyMessages - dailyMessageCount);
    }
    // Paid active plans have "unlimited" messages in this context
    return 999999999; // FIXED: Use large sentinel instead of Infinity
  }, [subscription, dailyMessageCount]);

  const messagesRemaining = useMemo(() => {
    return getMessagesRemaining();
  }, [getMessagesRemaining]);

  const getPlan = (): SubscriptionPlan => {
    const currentPlan = subscription?.plan || 'free';
    const isActive = subscription
      ? ['active', 'trialing'].includes(subscription.status)
      : false;

    // If the subscription exists but isn't active, treat as free
    if (subscription && !isActive) {
      return 'free';
    }
    // Otherwise return the plan name (or 'free' if no subscription)
    return currentPlan as SubscriptionPlan;
  };

  const refreshSubscription = () => {
    fetchSubscriptionData();
  };

  const handleMessageSent = () => {
    // Increment local count for free users or inactive subscribers
    const currentPlan = subscription?.plan || 'free';
    const isActive = subscription
      ? ['active', 'trialing'].includes(subscription.status)
      : false;

    if (currentPlan === 'free' || !isActive) {
      setDailyMessageCount((prev) => prev + 1);
    }

    // Refresh quota status after message sent
    fetchQuotaStatus();
  };

  const canUseComparison = useMemo(() => {
    const currentPlan = subscription?.plan || 'free';
    const isActive = subscription
      ? ['active', 'trialing'].includes(subscription.status)
      : false;

    return (
      currentPlan === 'premium' &&
      isActive &&
      (quotaStatus?.comparisons?.remaining ?? 0) > 0
    );
  }, [subscription, quotaStatus]);

  const canCreateWorkspace = useMemo(() => {
    const currentPlan = subscription?.plan || 'free';
    const isActive = subscription
      ? ['active', 'trialing'].includes(subscription.status)
      : false;

    // If subscription exists but isn't active, treat as free
    const effectivePlan = subscription && !isActive ? 'free' : currentPlan;
    const planDetails = SUBSCRIPTION_PLANS[effectivePlan];

    return (
      planDetails.maxWorkspaces === 999999999 ||
      workspaceCount < planDetails.maxWorkspaces
    );
  }, [subscription, workspaceCount]);

  const canUsePromptLibrary = useMemo(() => {
    const currentPlan = subscription?.plan || 'free';
    const isActive = subscription
      ? ['active', 'trialing'].includes(subscription.status)
      : false;

    // If subscription exists but isn't active, treat as free
    const effectivePlan = subscription && !isActive ? 'free' : currentPlan;
    const planDetails = SUBSCRIPTION_PLANS[effectivePlan];

    return planDetails.allowsPromptLibrary;
  }, [subscription]);

  const canUsePromptEnhancement = useMemo(() => {
    const currentPlan = subscription?.plan || 'free';
    const isActive = subscription
      ? ['active', 'trialing'].includes(subscription.status)
      : false;

    // If subscription exists but isn't active, treat as free
    const effectivePlan = subscription && !isActive ? 'free' : currentPlan;
    const planDetails = SUBSCRIPTION_PLANS[effectivePlan];

    return planDetails.allowsPromptEnhancement;
  }, [subscription]);

  const refreshQuota = () => {
    fetchQuotaStatus();
  };

  const value = {
    isLoading,
    subscription,
    canSendMessage,
    canAccessModelTier,
    canAccessPremiumModels,
    messagesRemaining,
    getPlan,
    refreshSubscription,
    handleMessageSent,
    accessibleModelIds,
    quotaStatus,
    canUseComparison,
    refreshQuota,
    workspaceCount,
    canCreateWorkspace,
    canUsePromptLibrary,
    canUsePromptEnhancement,
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
}

// Custom hook to use the subscription context
export function useSubscription() {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    log.error('useSubscription must be used within a SubscriptionProvider');
  }
  return context as SubscriptionContextValue;
}

// Type exports for convenience elsewhere if needed
export type { SubscriptionContextValue };
