import { TourProvider as ReactTourProvider } from '@reactour/tour';
import { ReactNode } from 'react';
import { useTour } from '@/hooks/useTour';

const tourConfig = {
  steps: [],
  styles: {
    popover: (base: any) => ({
      ...base,
      '--reactour-accent': 'hsl(var(--primary))',
      borderRadius: '8px',
      backgroundColor: 'hsl(var(--background))',
      color: 'hsl(var(--foreground))',
      border: '1px solid hsl(var(--border))',
      boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    }),
    maskArea: (base: any) => ({
      ...base,
      rx: 4,
    }),
    badge: (base: any) => ({
      ...base,
      backgroundColor: 'hsl(var(--primary))',
      color: 'hsl(var(--primary-foreground))',
    }),
  },
  padding: { mask: 10, popover: [8, 12] },
  disableInteraction: false,
  disableKeyboardNavigation: false,
  className: 'tour-popover',
  maskClassName: 'tour-mask',
  highlightedMaskClassName: 'tour-highlight',
  prevButton: ({ onClick }: any) => (
    <button
      onClick={onClick}
      className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded hover:bg-secondary/80"
    >
      Previous
    </button>
  ),
  nextButton: ({ onClick }: any) => (
    <button
      onClick={onClick}
      className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded hover:bg-primary/90"
    >
      Next
    </button>
  ),
  closeButton: ({ onClick }: any) => (
    <button
      onClick={onClick}
      className="absolute top-2 right-2 p-1 text-muted-foreground hover:text-foreground"
      aria-label="Close tour"
    >
      ✕
    </button>
  ),
};

function TourContent({ children }: { children: ReactNode }) {
  const { completeTour } = useTour();

  return (
    <ReactTourProvider
      {...tourConfig}
      onClickClose={completeTour}
      afterOpen={() => {
        // Analytics tracking
        if (typeof window !== 'undefined' && (window as any).gtag) {
          (window as any).gtag('event', 'tour_started', {
            event_category: 'onboarding',
          });
        }
      }}
      beforeClose={completeTour}
    >
      {children}
    </ReactTourProvider>
  );
}

export function TourProvider({ children }: { children: ReactNode }) {
  return <TourContent>{children}</TourContent>;
}
