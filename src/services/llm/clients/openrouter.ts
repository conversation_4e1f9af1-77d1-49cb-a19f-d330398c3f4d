import { CompletionOptions, Message } from '../types';
import { AppError, ErrorCode } from '@/lib/error';
import { Model } from '@/lib/supabase/db';
import { logger } from '@/lib/logger';
import { StreamProcessor } from './openrouter/stream/StreamProcessor';
import { ResourceManager } from './openrouter/stream/ResourceManager';
import { RetryHandler } from './openrouter/stream/RetryHandler';
import { MetricsCollector } from './openrouter/stream/MetricsCollector';
import {
  OpenRouterMessage,
  OpenRouterMessageContentPart,
  OpenRouterCompletionOptions,
  OpenRouterResponse,
} from './openrouter/types';

export class OpenRouterClient {
  private apiKey: string;
  private baseUrl: string;
  private siteUrl?: string;
  private appName?: string;
  private log = logger.child({ service: 'OpenRouterClient' });
  private readonly maxConcurrentFileFetches = 3;
  private readonly maxFileSize = 10 * 1024 * 1024; // 10MB
  private activeFileFetches = new Set<Promise<unknown>>();
  private defaultTimeoutMs: number;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://openrouter.ai/api/v1';
    this.siteUrl = process.env.OPENROUTER_SITE_URL;
    this.appName = process.env.OPENROUTER_APP_NAME || 'SabiChat';

    // Validate and parse the timeout value
    const rawTimeout = process.env.OPENROUTER_DEFAULT_TIMEOUT_MS;
    const parsedTimeout = parseInt(rawTimeout || '', 10);
    this.defaultTimeoutMs =
      !isNaN(parsedTimeout) && parsedTimeout > 0 ? parsedTimeout : 30000; // Default to 30 seconds if invalid
  }

  private isPrivateIP(hostname: string): boolean {
    // Check for IPv4 private ranges
    const ipv4Regex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
    const match = hostname.match(ipv4Regex);

    if (match) {
      const [, a, b, c, d] = match.map(Number);

      // Validate IP format
      if (a > 255 || b > 255 || c > 255 || d > 255) {
        return true; // Invalid IP, treat as private for safety
      }

      // Private ranges:
      // 10.0.0.0/8 (10.0.0.0 - **************)
      if (a === 10) return true;

      // **********/12 (********** - **************)
      if (a === 172 && b >= 16 && b <= 31) return true;

      // ***********/16 (*********** - ***************)
      if (a === 192 && b === 168) return true;

      // Loopback (*********/8)
      if (a === 127) return true;

      // Link-local (***********/16)
      if (a === 169 && b === 254) return true;

      // Multicast (*********/4)
      if (a >= 224 && a <= 239) return true;

      // Reserved (240.0.0.0/4)
      if (a >= 240) return true;

      // 0.0.0.0/8
      if (a === 0) return true;
    }

    // Check for localhost variants
    const localhostPatterns = ['localhost', '0.0.0.0', '::1', '::'];

    return localhostPatterns.includes(hostname.toLowerCase());
  }

  private validateURL(url: string): void {
    let parsedURL: URL;

    try {
      parsedURL = new URL(url);
    } catch {
      throw new AppError('Invalid URL format', ErrorCode.AI_COMPLETION, 400);
    }

    // Require HTTPS
    if (parsedURL.protocol !== 'https:') {
      throw new AppError(
        'Only HTTPS URLs are allowed for file fetching',
        ErrorCode.AI_COMPLETION,
        400
      );
    }

    // Block private/internal IPs
    if (this.isPrivateIP(parsedURL.hostname)) {
      throw new AppError(
        'Access to private/internal URLs is not allowed',
        ErrorCode.AI_COMPLETION,
        403
      );
    }

    // Additional hostname checks
    const hostname = parsedURL.hostname.toLowerCase();

    // Block common internal domains
    const blockedDomains = [
      'metadata.google.internal',
      'metadata',
      'localhost',
      'local',
    ];

    if (blockedDomains.some((blocked) => hostname.includes(blocked))) {
      throw new AppError(
        'Access to blocked domains is not allowed',
        ErrorCode.AI_COMPLETION,
        403
      );
    }
  }

  private async fetchAndEncodeFile(
    url: string,
    resourceManager?: ResourceManager
  ): Promise<string | null> {
    try {
      // Validate URL for security (prevent SSRF attacks)
      this.validateURL(url);

      // Wait for a slot to become available. This is a simple semaphore.
      while (this.activeFileFetches.size >= this.maxConcurrentFileFetches) {
        this.log.warn('Concurrent file fetch limit reached, waiting for slot');
        try {
          // Wait for any promise to complete (success or failure)
          // Ignore rejections since they just mean a slot opened up
          await Promise.race(this.activeFileFetches);
        } catch {
          // Ignore errors - rejection just means a slot is available
        }
      }

      this.log.info(`Fetching file from URL: ${url}`);

      // Create a fetch promise and add it to active fetches
      const fetchPromise = (async () => {
        try {
          const response = await fetch(url, {
            signal: resourceManager?.getSignal(),
          });
          if (!response.ok) {
            this.log.error(
              `Failed to fetch file from URL: ${url}, status: ${response.status}`
            );
            return null;
          }

          // Check content length if available
          const contentLength = response.headers.get('content-length');
          if (contentLength) {
            const size = parseInt(contentLength, 10);
            if (size > this.maxFileSize) {
              this.log.error(`File too large: ${size} bytes`);
              return null;
            }
          }

          // Stream the response to avoid loading entire file into memory
          const chunks: Uint8Array[] = [];
          let totalSize = 0;
          const reader = response.body?.getReader();

          if (!reader) {
            this.log.error('Response body is null');
            return null;
          }

          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            totalSize += value.length;
            if (totalSize > this.maxFileSize) {
              this.log.error(
                `File size exceeded limit during streaming: ${totalSize} bytes`
              );
              return null;
            }

            chunks.push(value);
          }

          const completeBuffer = Buffer.concat(chunks, totalSize);
          const base64 = completeBuffer.toString('base64');
          const mimeType =
            response.headers.get('content-type') || 'application/pdf';

          this.log.info(
            `Successfully encoded file (${mimeType}) from URL: ${url}`
          );
          return `data:${mimeType};base64,${base64}`;
        } catch (error) {
          this.log.error(
            { err: error },
            `Error fetching and encoding file: ${url}`
          );
          return null;
        }
      })();

      // Track the in-flight fetch and make sure we release the slot as soon as the
      // underlying promise settles (regardless of whether *this* call has awaited
      // it yet). This removes the tiny race window where other callers could keep
      // spinning even after a download has finished.
      this.activeFileFetches.add(fetchPromise);
      fetchPromise.finally(() => {
        this.activeFileFetches.delete(fetchPromise);
      });

      const result = await fetchPromise;
      return result as string | null;
    } catch (error) {
      this.log.error({ err: error }, `Error in file fetch operation: ${url}`);
      return null;
    }
  }

  private async formatMessages(
    messages: Message[],
    resourceManager?: ResourceManager,
    systemPrompt?: string
  ): Promise<OpenRouterMessage[]> {
    // Prepend system message if system prompt is provided
    const messagesToFormat = systemPrompt
      ? [{ role: 'system' as const, content: systemPrompt }, ...messages]
      : messages;

    const formattedMessagesPromises = messagesToFormat.map(async (message) => {
      let formattedContent: string | OpenRouterMessageContentPart[];

      // Handle content based on type (string or array of content parts)
      if (typeof message.content === 'string') {
        formattedContent = message.content;
      } else {
        // Handle array of content parts (text, images, files)
        const contentPartPromises = message.content.map(async (part) => {
          if (part.type === 'text') {
            return {
              type: 'text',
              text: part.text,
            } as OpenRouterMessageContentPart;
          } else if (part.type === 'image_url') {
            // Handle image URLs
            if (typeof part.image_url === 'string') {
              return {
                type: 'image_url',
                image_url: { url: part.image_url },
              } as OpenRouterMessageContentPart;
            } else {
              return {
                type: 'image_url',
                image_url: part.image_url,
              } as OpenRouterMessageContentPart;
            }
          } else if (part.type === 'file') {
            // Handle file uploads (PDFs)
            let fileData = '';

            // If base64_data is provided, use it directly
            if (part.base64_data) {
              this.log.info(
                `Using provided base64 data for file: ${part.filename}`
              );
              fileData = `data:${part.mime_type || 'application/pdf'};base64,${part.base64_data}`;
            }
            // If URL is provided, fetch and encode the file
            else if (part.url) {
              this.log.info(`Processing file from URL: ${part.url}`);
              const encodedFile = await this.fetchAndEncodeFile(
                part.url,
                resourceManager
              );
              if (encodedFile) {
                fileData = encodedFile;
              } else {
                this.log.error(`Failed to encode file from URL: ${part.url}`);
                // Return a text part explaining the error instead
                return {
                  type: 'text',
                  text: `[Error: Could not process file ${part.filename || 'document.pdf'}]`,
                } as OpenRouterMessageContentPart;
              }
            } else {
              this.log.warn(
                `File attachment missing both URL and base64 data: ${part.filename || 'unknown'}`
              );
              return {
                type: 'text',
                text: `[Error: Missing file content for ${part.filename || 'document.pdf'}]`,
              } as OpenRouterMessageContentPart;
            }

            // Ensure fileData is not empty
            if (!fileData) {
              this.log.error(
                `File data is empty for: ${part.filename || 'document.pdf'}`
              );
              return {
                type: 'text',
                text: `[Error: Empty file data for ${part.filename || 'document.pdf'}]`,
              } as OpenRouterMessageContentPart;
            }

            return {
              type: 'file',
              file: {
                filename: part.filename || 'document.pdf',
                file_data: fileData,
              },
            } as OpenRouterMessageContentPart;
          }
          // For any other types, try to adapt them as best as possible
          return {
            type: 'text',
            text: JSON.stringify(part),
          } as OpenRouterMessageContentPart;
        });

        formattedContent = await Promise.all(contentPartPromises);
      }

      // Base message object
      const baseMessage: OpenRouterMessage = {
        role: message.role,
        content: formattedContent as string | OpenRouterMessageContentPart[], // Assert type
      };

      // Add file annotations if they exist (only for assistant messages)
      if (
        message.role === 'assistant' &&
        message.file_annotations &&
        message.file_annotations.length > 0
      ) {
        baseMessage.annotations = message.file_annotations;
      }

      return baseMessage;
    });

    return Promise.all(formattedMessagesPromises);
  }

  private getRequestHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${this.apiKey}`,
    };

    if (this.siteUrl) {
      headers['HTTP-Referer'] = this.siteUrl;
    }

    if (this.appName) {
      headers['X-Title'] = this.appName;
    }

    return headers;
  }

  private getModelIdentifier(model: Model): string {
    if (!model.provider?.openrouter_name) {
      throw new AppError(
        'Provider name not found for model',
        ErrorCode.AI_COMPLETION,
        500
      );
    }

    return `${model.provider.openrouter_name}/${model.openrouter_name}`;
  }

  private configureWebSearch(
    requestOptions: OpenRouterCompletionOptions,
    useWebSearch: boolean
  ): void {
    if (useWebSearch) {
      // Configure via plugins
      requestOptions.plugins = [
        {
          id: 'web',
          max_results: 5,
        },
      ];
    }
  }

  private configurePDFParser(
    requestOptions: OpenRouterCompletionOptions,
    engine: string
  ): void {
    // Initialize plugins array if it doesn't exist
    if (!requestOptions.plugins) {
      requestOptions.plugins = [];
    }

    // Add or update the file-parser plugin configuration
    const fileParserPlugin = requestOptions.plugins.find(
      (p) => p.id === 'file-parser'
    );
    if (fileParserPlugin) {
      fileParserPlugin.pdf = { engine };
    } else {
      requestOptions.plugins.push({
        id: 'file-parser',
        pdf: { engine },
      });
    }
  }

  /**
   * Apply supported parameters to request options based on model configuration
   */
  private applySupportedParameters(
    requestOptions: OpenRouterCompletionOptions,
    options: CompletionOptions
  ): void {
    const supportedParams = options.model.supported_parameters;
    // const providerConfig = options.model
    //   .provider_specific_data as ProviderSpecificData;

    // Handle both JSON array and string array formats
    let paramsList: string[] = [];
    if (supportedParams) {
      if (Array.isArray(supportedParams)) {
        paramsList = supportedParams.filter(
          (param): param is string => typeof param === 'string'
        );
      } else if (typeof supportedParams === 'object') {
        // Handle case where it might be a JSON object containing an array
        try {
          const parsed = Array.isArray(supportedParams)
            ? supportedParams
            : Object.values(supportedParams);
          if (Array.isArray(parsed)) {
            paramsList = parsed.filter(
              (param): param is string => typeof param === 'string'
            );
          }
        } catch {
          // If parsing fails, just continue with empty array
        }
      }
    }

    if (paramsList.length === 0) {
      return;
    }

    // Apply additional parameters if supported by the model
    if (paramsList.includes('top_p') && options.topP !== undefined) {
      // Use top_p value if explicitly provided, don't couple with temperature
      requestOptions.top_p = options.topP;
    }

    if (paramsList.includes('top_k')) {
      // Set a reasonable top_k value
      requestOptions.top_k = 40;
    }

    if (
      paramsList.includes('temperature') &&
      options.temperature !== undefined
    ) {
      requestOptions.temperature = options.temperature;
    }

    if (paramsList.includes('tools') && options.useImageGeneration) {
      requestOptions.tools = [
        {
          type: 'function',
          function: {
            name: 'generate_image',
            description: 'Create an image',
            parameters: {
              type: 'object',
              properties: {
                prompt: {
                  type: 'string',
                  description:
                    'The text prompt describing the image to generate',
                },
                size: {
                  type: 'string',
                  enum: ['512x512', '768x768', '1024x1024'],
                  default: '768x768',
                  description: 'The size of the image to generate',
                },
              },
              required: ['prompt'],
            },
          },
        },
      ];
    }

    if (
      paramsList.includes('stop') &&
      options.stopSequences &&
      options.stopSequences.length > 0
    ) {
      requestOptions.stop = options.stopSequences;
    }

    if (paramsList.includes('max_tokens')) {
      // Use model's max_completion_tokens if available, otherwise fall back to options or reasonable default
      const modelMaxTokens = options.model.max_tokens;
      const defaultMaxTokens = modelMaxTokens
        ? Math.min(modelMaxTokens, 4096)
        : 2048;
      requestOptions.max_tokens = options.maxTokens ?? defaultMaxTokens;
    }

    // Add more parameter mappings as needed
    // Note: We could extend CompletionOptions to include these parameters explicitly
  }

  private applySupportedCapabilities(
    requestOptions: OpenRouterCompletionOptions,
    options: CompletionOptions,
    formattedMessages: OpenRouterMessage[]
  ): void {
    if (options.useWebSearch) {
      this.configureWebSearch(requestOptions, true);
    }

    if (
      options.model.capabilities?.file_upload &&
      formattedMessages.some(
        (msg) =>
          Array.isArray(msg.content) &&
          msg.content.some((part) => part.type === 'file')
      )
    ) {
      this.configurePDFParser(
        requestOptions,
        options.pdfParsingEngine || 'pdf-text'
      );
    }
  }

  public async generateCompletion(
    messages: Message[],
    options: CompletionOptions
  ): Promise<string> {
    try {
      const formattedMessages = await this.formatMessages(
        messages,
        undefined,
        options.systemPrompt
      );
      const modelString = this.getModelIdentifier(options.model);

      const requestOptions: OpenRouterCompletionOptions = {
        model: modelString,
        messages: formattedMessages,
        temperature: options.temperature,
        max_tokens: options.maxTokens,
        stream: false,
      };

      // Apply supported parameters dynamically
      this.applySupportedParameters(requestOptions, options);

      // Configure web search if enabled and supported
      this.applySupportedCapabilities(
        requestOptions,
        options,
        formattedMessages
      );

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: this.getRequestHeaders(),
        body: JSON.stringify(requestOptions),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new AppError(
          `OpenRouter API error: ${error.error?.message || 'Unknown error'}`,
          ErrorCode.AI_COMPLETION,
          response.status
        );
      }

      const data = (await response.json()) as OpenRouterResponse;

      // Return the text content
      return data.choices[0].message.content;
    } catch (error) {
      this.log.error(
        { err: error },
        'Error generating completion with OpenRouter'
      );
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(
        `Failed to generate completion: ${(error as Error).message}`,
        ErrorCode.AI_COMPLETION,
        500
      );
    }
  }

  public async generateStreamingCompletion(
    messages: Message[],
    options: CompletionOptions,
    customTimeoutMs?: number
  ): Promise<ReadableStream> {
    const log = this.log.child({ function: 'generateStreamingCompletion' });
    const resourceManager = new ResourceManager(
      customTimeoutMs || this.defaultTimeoutMs
    );
    const retryHandler = new RetryHandler();
    const metricsCollector = new MetricsCollector();

    try {
      log.info(
        {
          model: options.model.id,
          provider: options.model.provider?.name,
          messageCount: messages.length,
        },
        'Generating streaming completion'
      );

      const formattedMessages = await this.formatMessages(
        messages,
        resourceManager,
        options.systemPrompt
      );
      const modelIdentifier = this.getModelIdentifier(options.model);

      const requestOptions: OpenRouterCompletionOptions = {
        model: modelIdentifier,
        messages: formattedMessages,
        stream: true,
      };

      // Apply supported parameters dynamically
      this.applySupportedParameters(requestOptions, options);
      this.applySupportedCapabilities(
        requestOptions,
        options,
        formattedMessages
      );

      log.debug(
        { ...requestOptions, messages: null },
        'Sending request to OpenRouter'
      );

      const response = await retryHandler.withRetry(async () => {
        const res = await fetch(`${this.baseUrl}/chat/completions`, {
          method: 'POST',
          headers: this.getRequestHeaders(),
          body: JSON.stringify(requestOptions),
          signal: resourceManager.getSignal(),
        });

        if (!res.ok) {
          const errorBody = await res.text();
          log.error(
            {
              status: res.status,
              statusText: res.statusText,
              errorBody,
            },
            'OpenRouter API request failed'
          );
          throw new AppError(
            `OpenRouter API Error: ${res.statusText} - ${errorBody}`,
            ErrorCode.AI_STREAMING,
            res.status
          );
        }

        return res;
      });

      if (!response.body) {
        throw new AppError('Response body is null', ErrorCode.AI_STREAMING);
      }

      log.info('Received stream from OpenRouter');

      // Set up timeout for the entire stream
      resourceManager.setTimeout(() => {
        log.warn('Stream timeout reached');
        resourceManager.cleanup();
      });

      // Transform the OpenRouter SSE stream into our standardized NDJSON stream
      const streamProcessor = new StreamProcessor();
      const encoder = new TextEncoder();
      // Track first provider error so we don't log duplicates
      let providerErrorLogged = false;

      const transformStream = new TransformStream<Uint8Array, Uint8Array>({
        transform(chunk, controller) {
          try {
            metricsCollector.incrementBytes(chunk.length);
            metricsCollector.incrementChunks();

            const events = streamProcessor.process(chunk);
            for (const event of events) {
              if (event.type === 'tool') {
                metricsCollector.incrementToolCalls();
              } else if (event.type === 'error') {
                metricsCollector.incrementErrors();
                if (!providerErrorLogged) {
                  providerErrorLogged = true;
                  log.debug({ err: event.error }, 'Provider error event');
                }
                // fall through – we still forward the raw error event below
              }
              const payload = JSON.stringify(event);
              controller.enqueue(encoder.encode(payload + '\n'));
            }
          } catch (error) {
            log.warn({ err: error }, 'Failed to process OpenRouter chunk');
            metricsCollector.incrementErrors();
            controller.enqueue(
              encoder.encode(
                JSON.stringify({
                  type: 'error',
                  error: 'Failed to process stream chunk',
                  recoverable: true,
                }) + '\n'
              )
            );
          }
        },
        flush(controller) {
          try {
            log.info('Stream finished, flushing remaining data');
            const events = streamProcessor.flush();
            for (const event of events) {
              const payload = JSON.stringify(event);
              controller.enqueue(encoder.encode(payload + '\n'));
            }
            metricsCollector.logMetrics();
          } catch (error) {
            log.error({ err: error }, 'Error flushing stream');
            metricsCollector.incrementErrors();
            controller.enqueue(
              encoder.encode(
                JSON.stringify({
                  type: 'error',
                  error: 'Failed to flush stream',
                  recoverable: false,
                }) + '\n'
              )
            );
          } finally {
            resourceManager.cleanup();
          }
        },
      });

      return response.body.pipeThrough(transformStream);
    } catch (error) {
      log.error({ err: error }, 'Error in generateStreamingCompletion');
      resourceManager.cleanup();
      metricsCollector.logMetrics();

      if (error instanceof AppError) {
        throw error;
      }
      const cause = error instanceof Error ? error : undefined;
      throw new AppError(
        'Failed to stream completion from OpenRouter',
        ErrorCode.AI_STREAMING,
        500,
        cause
      );
    }
  }
}
