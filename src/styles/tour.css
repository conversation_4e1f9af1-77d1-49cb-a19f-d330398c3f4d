/* Tour-specific styles */
.tour-popover {
  max-width: 320px;
  z-index: 9999;
}

.tour-mask {
  z-index: 9998;
}

.tour-highlight {
  border-radius: 8px;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  .tour-popover {
    max-width: 280px;
    margin: 8px;
  }
  
  .tour-popover .space-y-3 {
    gap: 0.5rem;
  }
  
  .tour-popover h3 {
    font-size: 1rem;
  }
  
  .tour-popover p {
    font-size: 0.875rem;
  }
}

/* Dark mode adjustments */
.dark .tour-popover {
  background-color: hsl(var(--background));
  border-color: hsl(var(--border));
}

/* Animation for tour highlights */
.tour-highlight {
  animation: tour-pulse 2s infinite;
}

@keyframes tour-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(var(--primary-rgb), 0);
  }
}

/* Ensure tour elements are accessible */
.tour-popover {
  font-family: inherit;
}

.tour-popover button {
  font-family: inherit;
  transition: all 0.2s ease;
}

.tour-popover button:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Tour step content styling */
.tour-popover .space-y-3 > * + * {
  margin-top: 0.75rem;
}

/* Responsive adjustments for tablet */
@media (max-width: 768px) and (min-width: 641px) {
  .tour-popover {
    max-width: 300px;
  }
}
