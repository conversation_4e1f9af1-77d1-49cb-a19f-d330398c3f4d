export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: '12.2.3 (519615d)';
  };
  public: {
    Tables: {
      anonymous_sessions: {
        Row: {
          created_at: string | null;
          id: string;
          message_count: number | null;
          session_id: string;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          message_count?: number | null;
          session_id: string;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          message_count?: number | null;
          session_id?: string;
        };
        Relationships: [];
      };
      conversations: {
        Row: {
          anonymous_session_id: string | null;
          comparison_index: number | null;
          created_at: string | null;
          group_conversation_id: string;
          id: string;
          imported_from_share_id: string | null;
          is_active: boolean | null;
          is_comparison: boolean | null;
          last_model_used: string | null;
          state: Database['public']['Enums']['conversation_state'] | null;
          title: string | null;
          updated_at: string | null;
          user_id: string | null;
        };
        Insert: {
          anonymous_session_id?: string | null;
          comparison_index?: number | null;
          created_at?: string | null;
          group_conversation_id: string;
          id?: string;
          imported_from_share_id?: string | null;
          is_active?: boolean | null;
          is_comparison?: boolean | null;
          last_model_used?: string | null;
          state?: Database['public']['Enums']['conversation_state'] | null;
          title?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Update: {
          anonymous_session_id?: string | null;
          comparison_index?: number | null;
          created_at?: string | null;
          group_conversation_id?: string;
          id?: string;
          imported_from_share_id?: string | null;
          is_active?: boolean | null;
          is_comparison?: boolean | null;
          last_model_used?: string | null;
          state?: Database['public']['Enums']['conversation_state'] | null;
          title?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'conversations_anonymous_session_id_fkey';
            columns: ['anonymous_session_id'];
            isOneToOne: false;
            referencedRelation: 'anonymous_sessions';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'conversations_group_conversation_id_fkey';
            columns: ['group_conversation_id'];
            isOneToOne: false;
            referencedRelation: 'group_conversations';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_last_model_id';
            columns: ['last_model_used'];
            isOneToOne: false;
            referencedRelation: 'llm_models';
            referencedColumns: ['id'];
          },
        ];
      };
      feature_flag_user_overrides: {
        Row: {
          created_at: string;
          feature_flag_id: string;
          id: string;
          is_enabled: boolean;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          feature_flag_id: string;
          id?: string;
          is_enabled: boolean;
          user_id: string;
        };
        Update: {
          created_at?: string;
          feature_flag_id?: string;
          id?: string;
          is_enabled?: boolean;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'feature_flag_user_overrides_feature_flag_id_fkey';
            columns: ['feature_flag_id'];
            isOneToOne: false;
            referencedRelation: 'feature_flags';
            referencedColumns: ['id'];
          },
        ];
      };
      feature_flags: {
        Row: {
          created_at: string;
          created_by: string;
          description: string | null;
          display_name: string;
          id: string;
          is_enabled: boolean;
          name: string;
          rollout_percentage: number;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          created_by: string;
          description?: string | null;
          display_name: string;
          id?: string;
          is_enabled?: boolean;
          name: string;
          rollout_percentage?: number;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          created_by?: string;
          description?: string | null;
          display_name?: string;
          id?: string;
          is_enabled?: boolean;
          name?: string;
          rollout_percentage?: number;
          updated_at?: string;
        };
        Relationships: [];
      };
      group_conversations: {
        Row: {
          anonymous_session_id: string | null;
          created_at: string;
          id: string;
          is_active: boolean;
          is_comparison: boolean;
          is_favorite: boolean;
          is_temporary: boolean | null;
          title: string | null;
          updated_at: string | null;
          user_id: string | null;
          workspace_id: string | null;
        };
        Insert: {
          anonymous_session_id?: string | null;
          created_at?: string;
          id?: string;
          is_active?: boolean;
          is_comparison: boolean;
          is_favorite?: boolean;
          is_temporary?: boolean | null;
          title?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
          workspace_id?: string | null;
        };
        Update: {
          anonymous_session_id?: string | null;
          created_at?: string;
          id?: string;
          is_active?: boolean;
          is_comparison?: boolean;
          is_favorite?: boolean;
          is_temporary?: boolean | null;
          title?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
          workspace_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'group_conversations_anonymous_session_id_fkey';
            columns: ['anonymous_session_id'];
            isOneToOne: false;
            referencedRelation: 'anonymous_sessions';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'group_conversations_workspace_id_fkey';
            columns: ['workspace_id'];
            isOneToOne: false;
            referencedRelation: 'workspaces';
            referencedColumns: ['id'];
          },
        ];
      };
      image_credit_grants: {
        Row: {
          created_at: string | null;
          credits: number;
          expires_at: string | null;
          id: string;
          reason: string;
          used_credits: number | null;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          credits: number;
          expires_at?: string | null;
          id?: string;
          reason: string;
          used_credits?: number | null;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          credits?: number;
          expires_at?: string | null;
          id?: string;
          reason?: string;
          used_credits?: number | null;
          user_id?: string;
        };
        Relationships: [];
      };
      images: {
        Row: {
          created_at: string | null;
          height: number | null;
          id: string;
          message_id: string | null;
          prompt: string | null;
          provider_job_id: string | null;
          safety_score: number | null;
          url: string | null;
          user_id: string | null;
          width: number | null;
        };
        Insert: {
          created_at?: string | null;
          height?: number | null;
          id?: string;
          message_id?: string | null;
          prompt?: string | null;
          provider_job_id?: string | null;
          safety_score?: number | null;
          url?: string | null;
          user_id?: string | null;
          width?: number | null;
        };
        Update: {
          created_at?: string | null;
          height?: number | null;
          id?: string;
          message_id?: string | null;
          prompt?: string | null;
          provider_job_id?: string | null;
          safety_score?: number | null;
          url?: string | null;
          user_id?: string | null;
          width?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'images_message_id_fkey';
            columns: ['message_id'];
            isOneToOne: false;
            referencedRelation: 'messages';
            referencedColumns: ['id'];
          },
        ];
      };
      llm_models: {
        Row: {
          allows_file_upload: boolean | null;
          allows_search: boolean | null;
          allows_tool_usage: boolean | null;
          architecture: Json | null;
          capabilities: Json | null;
          config: Json;
          context_length: number | null;
          created_at: string | null;
          description: string | null;
          display_name: string;
          id: string;
          is_active: boolean | null;
          is_visible_by_default: boolean | null;
          last_synced: string | null;
          max_tokens: number | null;
          name: string;
          openrouter_name: string | null;
          pricing: Json | null;
          priority: number | null;
          provider_id: string | null;
          provider_specific_data: Json | null;
          supported_parameters: Json | null;
          tier: Database['public']['Enums']['subscription_plan'];
          updated_at: string | null;
        };
        Insert: {
          allows_file_upload?: boolean | null;
          allows_search?: boolean | null;
          allows_tool_usage?: boolean | null;
          architecture?: Json | null;
          capabilities?: Json | null;
          config?: Json;
          context_length?: number | null;
          created_at?: string | null;
          description?: string | null;
          display_name: string;
          id?: string;
          is_active?: boolean | null;
          is_visible_by_default?: boolean | null;
          last_synced?: string | null;
          max_tokens?: number | null;
          name: string;
          openrouter_name?: string | null;
          pricing?: Json | null;
          priority?: number | null;
          provider_id?: string | null;
          provider_specific_data?: Json | null;
          supported_parameters?: Json | null;
          tier?: Database['public']['Enums']['subscription_plan'];
          updated_at?: string | null;
        };
        Update: {
          allows_file_upload?: boolean | null;
          allows_search?: boolean | null;
          allows_tool_usage?: boolean | null;
          architecture?: Json | null;
          capabilities?: Json | null;
          config?: Json;
          context_length?: number | null;
          created_at?: string | null;
          description?: string | null;
          display_name?: string;
          id?: string;
          is_active?: boolean | null;
          is_visible_by_default?: boolean | null;
          last_synced?: string | null;
          max_tokens?: number | null;
          name?: string;
          openrouter_name?: string | null;
          pricing?: Json | null;
          priority?: number | null;
          provider_id?: string | null;
          provider_specific_data?: Json | null;
          supported_parameters?: Json | null;
          tier?: Database['public']['Enums']['subscription_plan'];
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'llm_models_provider_id_fkey';
            columns: ['provider_id'];
            isOneToOne: false;
            referencedRelation: 'llm_providers';
            referencedColumns: ['id'];
          },
        ];
      };
      llm_providers: {
        Row: {
          config: Json;
          created_at: string | null;
          display_name: string | null;
          id: string;
          is_active: boolean | null;
          name: string;
          openrouter_name: string | null;
          supports_native: boolean | null;
          supports_openrouter: boolean | null;
          updated_at: string | null;
        };
        Insert: {
          config?: Json;
          created_at?: string | null;
          display_name?: string | null;
          id?: string;
          is_active?: boolean | null;
          name: string;
          openrouter_name?: string | null;
          supports_native?: boolean | null;
          supports_openrouter?: boolean | null;
          updated_at?: string | null;
        };
        Update: {
          config?: Json;
          created_at?: string | null;
          display_name?: string | null;
          id?: string;
          is_active?: boolean | null;
          name?: string;
          openrouter_name?: string | null;
          supports_native?: boolean | null;
          supports_openrouter?: boolean | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      messages: {
        Row: {
          annotations: Json | null;
          attachments: Json | null;
          content: string;
          conversation_id: string | null;
          created_at: string | null;
          file_annotations: Json | null;
          id: string;
          metadata: Json;
          model_id: string | null;
          parent_message_id: string | null;
          provider_id: string | null;
          role: string;
          tokens_used: number | null;
          updated_at: string | null;
        };
        Insert: {
          annotations?: Json | null;
          attachments?: Json | null;
          content: string;
          conversation_id?: string | null;
          created_at?: string | null;
          file_annotations?: Json | null;
          id?: string;
          metadata?: Json;
          model_id?: string | null;
          parent_message_id?: string | null;
          provider_id?: string | null;
          role: string;
          tokens_used?: number | null;
          updated_at?: string | null;
        };
        Update: {
          annotations?: Json | null;
          attachments?: Json | null;
          content?: string;
          conversation_id?: string | null;
          created_at?: string | null;
          file_annotations?: Json | null;
          id?: string;
          metadata?: Json;
          model_id?: string | null;
          parent_message_id?: string | null;
          provider_id?: string | null;
          role?: string;
          tokens_used?: number | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'messages_conversation_id_fkey';
            columns: ['conversation_id'];
            isOneToOne: false;
            referencedRelation: 'conversations';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'messages_model_id_fkey';
            columns: ['model_id'];
            isOneToOne: false;
            referencedRelation: 'llm_models';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'messages_parent_message_id_fkey';
            columns: ['parent_message_id'];
            isOneToOne: false;
            referencedRelation: 'messages';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'messages_provider_id_fkey';
            columns: ['provider_id'];
            isOneToOne: false;
            referencedRelation: 'llm_providers';
            referencedColumns: ['id'];
          },
        ];
      };
      prompt_ratings: {
        Row: {
          created_at: string;
          prompt_id: string;
          rating: number;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          prompt_id: string;
          rating: number;
          user_id: string;
        };
        Update: {
          created_at?: string;
          prompt_id?: string;
          rating?: number;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'prompt_ratings_prompt_id_fkey';
            columns: ['prompt_id'];
            isOneToOne: false;
            referencedRelation: 'prompts';
            referencedColumns: ['id'];
          },
        ];
      };
      prompt_templates: {
        Row: {
          id: string;
          name: string | null;
          owner_id: string | null;
          template: string | null;
          workspace_id: string | null;
        };
        Insert: {
          id?: string;
          name?: string | null;
          owner_id?: string | null;
          template?: string | null;
          workspace_id?: string | null;
        };
        Update: {
          id?: string;
          name?: string | null;
          owner_id?: string | null;
          template?: string | null;
          workspace_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'prompt_templates_workspace_id_fkey';
            columns: ['workspace_id'];
            isOneToOne: false;
            referencedRelation: 'workspaces';
            referencedColumns: ['id'];
          },
        ];
      };
      prompt_usage: {
        Row: {
          id: string;
          inserted_at: string;
          prompt_id: string;
          sent: boolean;
          user_id: string;
        };
        Insert: {
          id?: string;
          inserted_at?: string;
          prompt_id: string;
          sent?: boolean;
          user_id: string;
        };
        Update: {
          id?: string;
          inserted_at?: string;
          prompt_id?: string;
          sent?: boolean;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'prompt_usage_prompt_id_fkey';
            columns: ['prompt_id'];
            isOneToOne: false;
            referencedRelation: 'prompts';
            referencedColumns: ['id'];
          },
        ];
      };
      prompt_versions: {
        Row: {
          body_md: string;
          created_at: string;
          edited_by: string;
          id: string;
          prompt_id: string;
          version: number;
        };
        Insert: {
          body_md: string;
          created_at?: string;
          edited_by: string;
          id?: string;
          prompt_id: string;
          version: number;
        };
        Update: {
          body_md?: string;
          created_at?: string;
          edited_by?: string;
          id?: string;
          prompt_id?: string;
          version?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'prompt_versions_prompt_id_fkey';
            columns: ['prompt_id'];
            isOneToOne: false;
            referencedRelation: 'prompts';
            referencedColumns: ['id'];
          },
        ];
      };
      prompts: {
        Row: {
          body_md: string;
          created_at: string;
          default_model: string | null;
          id: string;
          owner_id: string;
          rating_count: number;
          rating_sum: number;
          tags: string[] | null;
          title: string;
          updated_at: string | null;
          version: number;
          visibility: string;
          workspace_id: string | null;
        };
        Insert: {
          body_md: string;
          created_at?: string;
          default_model?: string | null;
          id?: string;
          owner_id: string;
          rating_count?: number;
          rating_sum?: number;
          tags?: string[] | null;
          title: string;
          updated_at?: string | null;
          version?: number;
          visibility: string;
          workspace_id?: string | null;
        };
        Update: {
          body_md?: string;
          created_at?: string;
          default_model?: string | null;
          id?: string;
          owner_id?: string;
          rating_count?: number;
          rating_sum?: number;
          tags?: string[] | null;
          title?: string;
          updated_at?: string | null;
          version?: number;
          visibility?: string;
          workspace_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'prompts_workspace_id_fkey';
            columns: ['workspace_id'];
            isOneToOne: false;
            referencedRelation: 'workspaces';
            referencedColumns: ['id'];
          },
        ];
      };
      rate_limits: {
        Row: {
          count: number;
          id: string;
          key: string;
          reset_time: string;
          updated_at: string | null;
        };
        Insert: {
          count?: number;
          id?: string;
          key: string;
          reset_time: string;
          updated_at?: string | null;
        };
        Update: {
          count?: number;
          id?: string;
          key?: string;
          reset_time?: string;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      shared_conversations: {
        Row: {
          created_at: string;
          id: string;
          last_original_message_id: string | null;
          original_conversation_id: string | null;
          shared_by_user_id: string | null;
          title: string | null;
        };
        Insert: {
          created_at?: string;
          id?: string;
          last_original_message_id?: string | null;
          original_conversation_id?: string | null;
          shared_by_user_id?: string | null;
          title?: string | null;
        };
        Update: {
          created_at?: string;
          id?: string;
          last_original_message_id?: string | null;
          original_conversation_id?: string | null;
          shared_by_user_id?: string | null;
          title?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'shared_conversations_last_original_message_id_fkey';
            columns: ['last_original_message_id'];
            isOneToOne: false;
            referencedRelation: 'messages';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'shared_conversations_original_conversation_id_fkey';
            columns: ['original_conversation_id'];
            isOneToOne: false;
            referencedRelation: 'conversations';
            referencedColumns: ['id'];
          },
        ];
      };
      shared_messages: {
        Row: {
          content: string | null;
          created_at: string;
          id: string;
          original_message_id: string | null;
          parent_shared_message_id: string | null;
          role: string;
          shared_conversation_id: string;
        };
        Insert: {
          content?: string | null;
          created_at: string;
          id?: string;
          original_message_id?: string | null;
          parent_shared_message_id?: string | null;
          role: string;
          shared_conversation_id: string;
        };
        Update: {
          content?: string | null;
          created_at?: string;
          id?: string;
          original_message_id?: string | null;
          parent_shared_message_id?: string | null;
          role?: string;
          shared_conversation_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'shared_messages_original_message_id_fkey';
            columns: ['original_message_id'];
            isOneToOne: false;
            referencedRelation: 'messages';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'shared_messages_parent_shared_message_id_fkey';
            columns: ['parent_shared_message_id'];
            isOneToOne: false;
            referencedRelation: 'shared_messages';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'shared_messages_shared_conversation_id_fkey';
            columns: ['shared_conversation_id'];
            isOneToOne: false;
            referencedRelation: 'shared_conversations';
            referencedColumns: ['id'];
          },
        ];
      };
      stripe_events: {
        Row: {
          created_at: string;
          event_data: Json | null;
          event_id: string;
          event_type: string | null;
          id: string;
        };
        Insert: {
          created_at?: string;
          event_data?: Json | null;
          event_id: string;
          event_type?: string | null;
          id?: string;
        };
        Update: {
          created_at?: string;
          event_data?: Json | null;
          event_id?: string;
          event_type?: string | null;
          id?: string;
        };
        Relationships: [];
      };
      subscription_plan_configs: {
        Row: {
          config: Json;
          created_at: string | null;
          id: string;
          is_active: boolean | null;
          plan_name: string;
          version: number;
        };
        Insert: {
          config: Json;
          created_at?: string | null;
          id?: string;
          is_active?: boolean | null;
          plan_name: string;
          version: number;
        };
        Update: {
          config?: Json;
          created_at?: string | null;
          id?: string;
          is_active?: boolean | null;
          plan_name?: string;
          version?: number;
        };
        Relationships: [];
      };
      subscriptions: {
        Row: {
          cancel_at_period_end: boolean;
          created_at: string | null;
          current_period_end: string;
          id: string;
          plan: Database['public']['Enums']['subscription_plan'];
          status: Database['public']['Enums']['subscription_status'];
          stripe_customer_id: string | null;
          stripe_subscription_id: string | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          cancel_at_period_end: boolean;
          created_at?: string | null;
          current_period_end: string;
          id: string;
          plan: Database['public']['Enums']['subscription_plan'];
          status: Database['public']['Enums']['subscription_status'];
          stripe_customer_id?: string | null;
          stripe_subscription_id?: string | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Update: {
          cancel_at_period_end?: boolean;
          created_at?: string | null;
          current_period_end?: string;
          id?: string;
          plan?: Database['public']['Enums']['subscription_plan'];
          status?: Database['public']['Enums']['subscription_status'];
          stripe_customer_id?: string | null;
          stripe_subscription_id?: string | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [];
      };
      supabase_migrations: {
        Row: {
          created_at: string | null;
          hash: string;
          id: number;
          name: string;
          version: string;
        };
        Insert: {
          created_at?: string | null;
          hash: string;
          id?: number;
          name: string;
          version: string;
        };
        Update: {
          created_at?: string | null;
          hash?: string;
          id?: number;
          name?: string;
          version?: string;
        };
        Relationships: [];
      };
      token_reservations: {
        Row: {
          actual_tokens: number | null;
          committed_at: string | null;
          created_at: string | null;
          estimated_tokens: number;
          expires_at: string;
          metadata: Json | null;
          model_id: string;
          reservation_id: string;
          status: string;
          tokens_reserved: number;
          user_id: string;
        };
        Insert: {
          actual_tokens?: number | null;
          committed_at?: string | null;
          created_at?: string | null;
          estimated_tokens: number;
          expires_at: string;
          metadata?: Json | null;
          model_id: string;
          reservation_id: string;
          status: string;
          tokens_reserved: number;
          user_id: string;
        };
        Update: {
          actual_tokens?: number | null;
          committed_at?: string | null;
          created_at?: string | null;
          estimated_tokens?: number;
          expires_at?: string;
          metadata?: Json | null;
          model_id?: string;
          reservation_id?: string;
          status?: string;
          tokens_reserved?: number;
          user_id?: string;
        };
        Relationships: [];
      };
      uploaded_files: {
        Row: {
          created_at: string | null;
          file_hash: string;
          id: string;
          mime_type: string | null;
          original_filename: string | null;
          size_bytes: number | null;
          storage_path: string;
          uploader_user_id: string | null;
        };
        Insert: {
          created_at?: string | null;
          file_hash: string;
          id?: string;
          mime_type?: string | null;
          original_filename?: string | null;
          size_bytes?: number | null;
          storage_path: string;
          uploader_user_id?: string | null;
        };
        Update: {
          created_at?: string | null;
          file_hash?: string;
          id?: string;
          mime_type?: string | null;
          original_filename?: string | null;
          size_bytes?: number | null;
          storage_path?: string;
          uploader_user_id?: string | null;
        };
        Relationships: [];
      };
      usage_logs: {
        Row: {
          completion_tokens: number | null;
          conversation_id: string | null;
          cost: number | null;
          created_at: string | null;
          id: string;
          message_id: string | null;
          metadata: Json;
          model_id: string | null;
          prompt_tokens: number | null;
          provider_id: string | null;
          status: string;
          tokens_used: number;
          user_id: string | null;
        };
        Insert: {
          completion_tokens?: number | null;
          conversation_id?: string | null;
          cost?: number | null;
          created_at?: string | null;
          id?: string;
          message_id?: string | null;
          metadata?: Json;
          model_id?: string | null;
          prompt_tokens?: number | null;
          provider_id?: string | null;
          status: string;
          tokens_used: number;
          user_id?: string | null;
        };
        Update: {
          completion_tokens?: number | null;
          conversation_id?: string | null;
          cost?: number | null;
          created_at?: string | null;
          id?: string;
          message_id?: string | null;
          metadata?: Json;
          model_id?: string | null;
          prompt_tokens?: number | null;
          provider_id?: string | null;
          status?: string;
          tokens_used?: number;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'usage_logs_conversation_id_fkey';
            columns: ['conversation_id'];
            isOneToOne: false;
            referencedRelation: 'conversations';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'usage_logs_message_id_fkey';
            columns: ['message_id'];
            isOneToOne: false;
            referencedRelation: 'messages';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'usage_logs_model_id_fkey';
            columns: ['model_id'];
            isOneToOne: false;
            referencedRelation: 'llm_models';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'usage_logs_provider_id_fkey';
            columns: ['provider_id'];
            isOneToOne: false;
            referencedRelation: 'llm_providers';
            referencedColumns: ['id'];
          },
        ];
      };
      user_balances: {
        Row: {
          created_at: string;
          credits_purchased_total: number;
          credits_used_from_purchased: number;
          id: string;
          tokens_purchased_total: number;
          tokens_used_from_purchased: number;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          credits_purchased_total?: number;
          credits_used_from_purchased?: number;
          id?: string;
          tokens_purchased_total?: number;
          tokens_used_from_purchased?: number;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          credits_purchased_total?: number;
          credits_used_from_purchased?: number;
          id?: string;
          tokens_purchased_total?: number;
          tokens_used_from_purchased?: number;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [];
      };
      user_daily_usage: {
        Row: {
          comparison_count: number | null;
          created_at: string | null;
          date: string;
          id: string;
          image_count: number | null;
          message_count: number;
          tokens_used: number | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          comparison_count?: number | null;
          created_at?: string | null;
          date: string;
          id?: string;
          image_count?: number | null;
          message_count: number;
          tokens_used?: number | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Update: {
          comparison_count?: number | null;
          created_at?: string | null;
          date?: string;
          id?: string;
          image_count?: number | null;
          message_count?: number;
          tokens_used?: number | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [];
      };
      user_monthly_usage: {
        Row: {
          comparison_count: number | null;
          created_at: string | null;
          id: string;
          image_credits_used: number | null;
          tokens_used: number | null;
          updated_at: string | null;
          user_id: string;
          year_month: string;
        };
        Insert: {
          comparison_count?: number | null;
          created_at?: string | null;
          id?: string;
          image_credits_used?: number | null;
          tokens_used?: number | null;
          updated_at?: string | null;
          user_id: string;
          year_month: string;
        };
        Update: {
          comparison_count?: number | null;
          created_at?: string | null;
          id?: string;
          image_credits_used?: number | null;
          tokens_used?: number | null;
          updated_at?: string | null;
          user_id?: string;
          year_month?: string;
        };
        Relationships: [];
      };
      user_preferences: {
        Row: {
          created_at: string;
          default_model_id: string | null;
          explicitly_hidden_model_ids: Json;
          explicitly_shown_model_ids: Json;
          hidden_model_ids: Json | null;
          id: number;
          updated_at: string | null;
          user_id: string;
          tour_completed: boolean;
          tour_completed_at: string | null;
          tour_version: number;
        };
        Insert: {
          created_at?: string;
          default_model_id?: string | null;
          explicitly_hidden_model_ids?: Json;
          explicitly_shown_model_ids?: Json;
          hidden_model_ids?: Json | null;
          id?: number;
          updated_at?: string | null;
          user_id?: string;
          tour_completed?: boolean;
          tour_completed_at?: string | null;
          tour_version?: number;
        };
        Update: {
          created_at?: string;
          default_model_id?: string | null;
          explicitly_hidden_model_ids?: Json;
          explicitly_shown_model_ids?: Json;
          hidden_model_ids?: Json | null;
          id?: number;
          updated_at?: string | null;
          user_id?: string;
          tour_completed?: boolean;
          tour_completed_at?: string | null;
          tour_version?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'user_preferences_default_model_id_fkey';
            columns: ['default_model_id'];
            isOneToOne: false;
            referencedRelation: 'llm_models';
            referencedColumns: ['id'];
          },
        ];
      };
      webhook_rate_limits: {
        Row: {
          created_at: string | null;
          expires_at: string;
          rate_key: string;
          request_count: number;
          updated_at: string | null;
          window_start: string;
        };
        Insert: {
          created_at?: string | null;
          expires_at: string;
          rate_key: string;
          request_count?: number;
          updated_at?: string | null;
          window_start?: string;
        };
        Update: {
          created_at?: string | null;
          expires_at?: string;
          rate_key?: string;
          request_count?: number;
          updated_at?: string | null;
          window_start?: string;
        };
        Relationships: [];
      };
      workspace_embeddings: {
        Row: {
          chunk_index: number | null;
          chunk_text: string | null;
          embedding: string | null;
          file_id: string | null;
          id: number;
          workspace_id: string | null;
        };
        Insert: {
          chunk_index?: number | null;
          chunk_text?: string | null;
          embedding?: string | null;
          file_id?: string | null;
          id?: number;
          workspace_id?: string | null;
        };
        Update: {
          chunk_index?: number | null;
          chunk_text?: string | null;
          embedding?: string | null;
          file_id?: string | null;
          id?: number;
          workspace_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'workspace_embeddings_file_id_fkey';
            columns: ['file_id'];
            isOneToOne: false;
            referencedRelation: 'workspace_files';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'workspace_embeddings_workspace_id_fkey';
            columns: ['workspace_id'];
            isOneToOne: false;
            referencedRelation: 'workspaces';
            referencedColumns: ['id'];
          },
        ];
      };
      workspace_facts: {
        Row: {
          created_at: string | null;
          file_id: string | null;
          id: number;
          key: string | null;
          value: Json | null;
          workspace_id: string | null;
        };
        Insert: {
          created_at?: string | null;
          file_id?: string | null;
          id?: number;
          key?: string | null;
          value?: Json | null;
          workspace_id?: string | null;
        };
        Update: {
          created_at?: string | null;
          file_id?: string | null;
          id?: number;
          key?: string | null;
          value?: Json | null;
          workspace_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'workspace_facts_file_id_fkey';
            columns: ['file_id'];
            isOneToOne: false;
            referencedRelation: 'workspace_files';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'workspace_facts_workspace_id_fkey';
            columns: ['workspace_id'];
            isOneToOne: false;
            referencedRelation: 'workspaces';
            referencedColumns: ['id'];
          },
        ];
      };
      workspace_files: {
        Row: {
          byte_size: number | null;
          created_at: string | null;
          file_path: string | null;
          filename: string | null;
          id: string;
          mime_type: string | null;
          status: string | null;
          text_content: string | null;
          workspace_id: string | null;
        };
        Insert: {
          byte_size?: number | null;
          created_at?: string | null;
          file_path?: string | null;
          filename?: string | null;
          id?: string;
          mime_type?: string | null;
          status?: string | null;
          text_content?: string | null;
          workspace_id?: string | null;
        };
        Update: {
          byte_size?: number | null;
          created_at?: string | null;
          file_path?: string | null;
          filename?: string | null;
          id?: string;
          mime_type?: string | null;
          status?: string | null;
          text_content?: string | null;
          workspace_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'workspace_files_workspace_id_fkey';
            columns: ['workspace_id'];
            isOneToOne: false;
            referencedRelation: 'workspaces';
            referencedColumns: ['id'];
          },
        ];
      };
      workspace_notes: {
        Row: {
          body: string | null;
          created_at: string | null;
          embedding: string | null;
          id: string;
          title: string | null;
          updated_at: string | null;
          workspace_id: string | null;
        };
        Insert: {
          body?: string | null;
          created_at?: string | null;
          embedding?: string | null;
          id?: string;
          title?: string | null;
          updated_at?: string | null;
          workspace_id?: string | null;
        };
        Update: {
          body?: string | null;
          created_at?: string | null;
          embedding?: string | null;
          id?: string;
          title?: string | null;
          updated_at?: string | null;
          workspace_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'workspace_notes_workspace_id_fkey';
            columns: ['workspace_id'];
            isOneToOne: false;
            referencedRelation: 'workspaces';
            referencedColumns: ['id'];
          },
        ];
      };
      workspace_questions: {
        Row: {
          id: string;
          question: string | null;
          workspace_id: string | null;
        };
        Insert: {
          id?: string;
          question?: string | null;
          workspace_id?: string | null;
        };
        Update: {
          id?: string;
          question?: string | null;
          workspace_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'workspace_questions_workspace_id_fkey';
            columns: ['workspace_id'];
            isOneToOne: false;
            referencedRelation: 'workspaces';
            referencedColumns: ['id'];
          },
        ];
      };
      workspaces: {
        Row: {
          created_at: string | null;
          default_model: string | null;
          default_model_id: string | null;
          description: string | null;
          icon: string | null;
          id: string;
          is_favorite: boolean | null;
          name: string;
          owner_id: string | null;
          status: Database['public']['Enums']['job_status'];
          structured: Json | null;
        };
        Insert: {
          created_at?: string | null;
          default_model?: string | null;
          default_model_id?: string | null;
          description?: string | null;
          icon?: string | null;
          id?: string;
          is_favorite?: boolean | null;
          name: string;
          owner_id?: string | null;
          status?: Database['public']['Enums']['job_status'];
          structured?: Json | null;
        };
        Update: {
          created_at?: string | null;
          default_model?: string | null;
          default_model_id?: string | null;
          description?: string | null;
          icon?: string | null;
          id?: string;
          is_favorite?: boolean | null;
          name?: string;
          owner_id?: string | null;
          status?: Database['public']['Enums']['job_status'];
          structured?: Json | null;
        };
        Relationships: [
          {
            foreignKeyName: 'workspaces_default_model_id_fkey';
            columns: ['default_model_id'];
            isOneToOne: false;
            referencedRelation: 'llm_models';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      add_persistent_credit_balance: {
        Args: { p_user_id: string; p_credits: number };
        Returns: undefined;
      };
      add_persistent_token_balance: {
        Args: { p_user_id: string; p_tokens: number };
        Returns: undefined;
      };
      atomic_comparison_check_and_increment: {
        Args: { p_user_id: string; p_date: string; p_daily_limit: number };
        Returns: {
          success: boolean;
          remaining: number;
        }[];
      };
      atomic_comparison_check_and_increment_safe: {
        Args: { p_user_id: string; p_date: string; p_daily_limit: number };
        Returns: {
          success: boolean;
          remaining: number;
        }[];
      };
      atomic_image_check_and_increment: {
        Args: {
          p_user_id: string;
          p_credits: number;
          p_year_month: string;
          p_quota_limit: number;
        };
        Returns: {
          success: boolean;
          remaining: number;
        }[];
      };
      atomic_image_check_and_increment_safe: {
        Args: {
          p_user_id: string;
          p_credits: number;
          p_year_month: string;
          p_quota_limit: number;
        };
        Returns: {
          success: boolean;
          remaining: number;
        }[];
      };
      atomic_token_check_and_increment: {
        Args: {
          p_user_id: string;
          p_tokens: number;
          p_year_month: string;
          p_quota_limit: number;
        };
        Returns: {
          success: boolean;
          remaining: number;
        }[];
      };
      atomic_token_check_and_increment_safe: {
        Args: {
          p_user_id: string;
          p_tokens: number;
          p_year_month: string;
          p_quota_limit: number;
        };
        Returns: {
          success: boolean;
          remaining: number;
        }[];
      };
      binary_quantize: {
        Args: { '': string } | { '': unknown };
        Returns: unknown;
      };
      check_and_increment_webhook_rate_limit: {
        Args: {
          p_rate_key: string;
          p_max_requests?: number;
          p_window_seconds?: number;
        };
        Returns: {
          allowed: boolean;
          current_count: number;
          reset_time: string;
        }[];
      };
      cleanup_expired_rate_limits: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      cleanup_expired_token_reservations: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      cleanup_expired_webhook_rate_limits: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      cleanup_monthly_purchase_tracking: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      commit_token_reservation: {
        Args: { p_reservation_id: string; p_actual_tokens?: number };
        Returns: {
          success: boolean;
          delta_tokens: number;
        }[];
      };
      create_token_reservation: {
        Args: {
          p_user_id: string;
          p_tokens: number;
          p_model_id: string;
          p_reservation_ttl_minutes?: number;
        };
        Returns: {
          reservation_id: string;
          success: boolean;
        }[];
      };
      decrement_daily_token_usage: {
        Args:
          | { p_user_id: string; p_date: string; p_tokens: number }
          | { p_user_id: string; p_date: string; p_tokens: number };
        Returns: undefined;
      };
      decrement_monthly_image_usage: {
        Args: { p_user_id: string; p_year_month: string; p_credits: number };
        Returns: undefined;
      };
      decrement_monthly_token_usage: {
        Args: { p_user_id: string; p_year_month: string; p_tokens: number };
        Returns: undefined;
      };
      decrement_user_comparison_count: {
        Args: { p_user_id: string; p_date: string };
        Returns: undefined;
      };
      decrement_user_image_count: {
        Args: { p_user_id: string; p_date: string };
        Returns: undefined;
      };
      find_subscription_by_customer_id: {
        Args: { p_stripe_customer_id: string };
        Returns: {
          id: string;
          user_id: string;
          plan: string;
          status: string;
          current_period_end: string;
          cancel_at_period_end: boolean;
          created_at: string;
          updated_at: string;
          stripe_customer_id: string;
          stripe_subscription_id: string;
        }[];
      };
      get_reservation_stats: {
        Args: Record<PropertyKey, never>;
        Returns: {
          pending_count: number;
          expired_count: number;
          committed_count: number;
          refunded_count: number;
          total_tokens_reserved: number;
        }[];
      };
      get_user_persistent_balance: {
        Args: { p_user_id: string };
        Returns: {
          tokens_available: number;
          tokens_purchased_total: number;
          tokens_used_total: number;
          credits_available: number;
          credits_purchased_total: number;
          credits_used_total: number;
        }[];
      };
      gtrgm_compress: {
        Args: { '': unknown };
        Returns: unknown;
      };
      gtrgm_decompress: {
        Args: { '': unknown };
        Returns: unknown;
      };
      gtrgm_in: {
        Args: { '': unknown };
        Returns: unknown;
      };
      gtrgm_options: {
        Args: { '': unknown };
        Returns: undefined;
      };
      gtrgm_out: {
        Args: { '': unknown };
        Returns: unknown;
      };
      halfvec_avg: {
        Args: { '': number[] };
        Returns: unknown;
      };
      halfvec_out: {
        Args: { '': unknown };
        Returns: unknown;
      };
      halfvec_send: {
        Args: { '': unknown };
        Returns: string;
      };
      halfvec_typmod_in: {
        Args: { '': unknown[] };
        Returns: number;
      };
      hnsw_bit_support: {
        Args: { '': unknown };
        Returns: unknown;
      };
      hnsw_halfvec_support: {
        Args: { '': unknown };
        Returns: unknown;
      };
      hnsw_sparsevec_support: {
        Args: { '': unknown };
        Returns: unknown;
      };
      hnswhandler: {
        Args: { '': unknown };
        Returns: unknown;
      };
      increment_comparison_count: {
        Args: { p_user_id: string; p_year_month: string };
        Returns: undefined;
      };
      increment_daily_token_usage: {
        Args:
          | { p_user_id: string; p_date: string; p_tokens: number }
          | { p_user_id: string; p_date: string; p_tokens: number };
        Returns: {
          success: boolean;
        }[];
      };
      increment_daily_token_usage_with_limit: {
        Args:
          | {
              p_user_id: string;
              p_date: string;
              p_tokens: number;
              p_daily_limit?: number;
            }
          | {
              p_user_id: string;
              p_date: string;
              p_tokens: number;
              p_daily_limit?: number;
            };
        Returns: {
          success: boolean;
          remaining: number;
        }[];
      };
      increment_monthly_token_usage: {
        Args: { p_user_id: string; p_year_month: string; p_tokens: number };
        Returns: undefined;
      };
      increment_user_comparison_count: {
        Args: { p_user_id: string; p_date: string };
        Returns: {
          success: boolean;
        }[];
      };
      increment_user_image_count: {
        Args: { p_user_id: string; p_date: string };
        Returns: undefined;
      };
      increment_user_message_count: {
        Args: { p_user_id: string; p_date: string };
        Returns: {
          success: boolean;
        }[];
      };
      initialize_chat_session: {
        Args: {
          p_user_id: string;
          p_message_content: string;
          p_model_id: string;
          p_provider_id: string;
          p_conversation_id?: string;
          p_group_conversation_id?: string;
          p_parent_message_id?: string;
          p_attachments?: Json;
          p_workspace_id?: string;
          p_is_comparison?: boolean;
          p_is_temporary?: boolean;
          p_comparison_index?: number;
          p_group_title?: string;
        };
        Returns: {
          conversation_id: string;
          group_conversation_id: string;
          user_message_id: string;
          assistant_message_id: string;
          is_new_conversation: boolean;
        }[];
      };
      is_valid_session: {
        Args: { session_id: string };
        Returns: boolean;
      };
      ivfflat_bit_support: {
        Args: { '': unknown };
        Returns: unknown;
      };
      ivfflat_halfvec_support: {
        Args: { '': unknown };
        Returns: unknown;
      };
      ivfflathandler: {
        Args: { '': unknown };
        Returns: unknown;
      };
      l2_norm: {
        Args: { '': unknown } | { '': unknown };
        Returns: number;
      };
      l2_normalize: {
        Args: { '': string } | { '': unknown } | { '': unknown };
        Returns: string;
      };
      match_chunks: {
        Args: {
          p_workspace: string;
          p_query: string;
          p_topk?: number;
          p_min_sim?: number;
        };
        Returns: {
          id: number;
          chunk_text: string;
          similarity: number;
        }[];
      };
      match_facts: {
        Args: { p_workspace: string; p_search: string; p_topk?: number };
        Returns: {
          key: string;
          value: Json;
        }[];
      };
      match_notes: {
        Args: { p_workspace: string; p_query: string; p_topk?: number };
        Returns: {
          id: string;
          body: string;
          similarity: number;
        }[];
      };
      process_stripe_webhook: {
        Args: { p_event_id: string; p_event_type: string; p_event_data?: Json };
        Returns: {
          already_processed: boolean;
        }[];
      };
      refund_persistent_token_balance: {
        Args: { p_user_id: string; p_tokens: number };
        Returns: {
          success: boolean;
          refunded: number;
        }[];
      };
      refund_token_reservation: {
        Args: { p_reservation_id: string };
        Returns: {
          success: boolean;
          tokens_to_refund: number;
        }[];
      };
      set_limit: {
        Args: { '': number };
        Returns: number;
      };
      show_limit: {
        Args: Record<PropertyKey, never>;
        Returns: number;
      };
      show_trgm: {
        Args: { '': string };
        Returns: string[];
      };
      sparsevec_out: {
        Args: { '': unknown };
        Returns: unknown;
      };
      sparsevec_send: {
        Args: { '': unknown };
        Returns: string;
      };
      sparsevec_typmod_in: {
        Args: { '': unknown[] };
        Returns: number;
      };
      use_image_credit_grant: {
        Args: { p_user_id: string; p_credits: number };
        Returns: boolean;
      };
      use_persistent_credit_balance: {
        Args: { p_user_id: string; p_credits: number };
        Returns: {
          success: boolean;
          remaining_balance: number;
        }[];
      };
      use_persistent_token_balance: {
        Args: { p_user_id: string; p_tokens: number };
        Returns: {
          success: boolean;
          remaining_balance: number;
        }[];
      };
      vector_avg: {
        Args: { '': number[] };
        Returns: string;
      };
      vector_dims: {
        Args: { '': string } | { '': unknown };
        Returns: number;
      };
      vector_norm: {
        Args: { '': string };
        Returns: number;
      };
      vector_out: {
        Args: { '': string };
        Returns: unknown;
      };
      vector_send: {
        Args: { '': string };
        Returns: string;
      };
      vector_typmod_in: {
        Args: { '': unknown[] };
        Returns: number;
      };
      verify_customer_ownership: {
        Args: { p_stripe_customer_id: string; p_user_id: string };
        Returns: boolean;
      };
    };
    Enums: {
      conversation_state: 'healthy' | 'error' | 'exceeds_limit';
      job_status: 'initiated' | 'pending' | 'completed' | 'error';
      subscription_plan: 'free' | 'starter' | 'premium';
      subscription_status:
        | 'active'
        | 'canceled'
        | 'incomplete'
        | 'past_due'
        | 'trialing';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DatabaseWithoutInternals = Omit<Database, '__InternalSupabase'>;

type DefaultSchema = DatabaseWithoutInternals[Extract<
  keyof Database,
  'public'
>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] &
        DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] &
        DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  public: {
    Enums: {
      conversation_state: ['healthy', 'error', 'exceeds_limit'],
      job_status: ['initiated', 'pending', 'completed', 'error'],
      subscription_plan: ['free', 'starter', 'premium'],
      subscription_status: [
        'active',
        'canceled',
        'incomplete',
        'past_due',
        'trialing',
      ],
    },
  },
} as const;
