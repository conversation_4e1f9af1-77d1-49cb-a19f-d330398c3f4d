-- Add tour completion tracking to user_preferences
-- This migration adds tour-related columns to track user onboarding progress

-- Add tour completion tracking columns
ALTER TABLE user_preferences 
ADD COLUMN IF NOT EXISTS tour_completed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS tour_completed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS tour_version INTEGER DEFAULT 1;

-- Create index for efficient tour status queries
CREATE INDEX IF NOT EXISTS idx_user_preferences_tour_completed ON user_preferences(tour_completed);

-- Update existing users to have tour_completed = false (for users who haven't seen the tour)
UPDATE user_preferences SET tour_completed = FALSE WHERE tour_completed IS NULL;

-- Add comment for documentation
COMMENT ON COLUMN user_preferences.tour_completed IS 'Whether the user has completed the onboarding tour';
COMMENT ON COLUMN user_preferences.tour_completed_at IS 'Timestamp when the user completed the tour';
COMMENT ON COLUMN user_preferences.tour_version IS 'Version of the tour that was completed (for future tour updates)';
