-- Workspace retrieval and ingestion optimizations
-- - Fix match_facts to scope by workspace and use FTS + trigram
-- - Use p_min_sim in match_chunks
-- - Add indexes for notes and embeddings vectors and filters
-- - Add trigram index for facts value and a GIN FTS index
-- - Normalize vectors on write
-- - Add note_id to embeddings table and a uniqueness constraint for idempotent upserts

-- Ensure required extensions
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS vector;

-- Create FTS generated column and index BEFORE using it in function
ALTER TABLE public.workspace_facts
  ADD COLUMN IF NOT EXISTS facts_tsv tsvector
  GENERATED ALWAYS AS (
    to_tsvector('english', coalesce(key,'') || ' ' || coalesce(value::text,''))
  ) STORED;

CREATE INDEX IF NOT EXISTS facts_tsv_idx
ON public.workspace_facts USING gin (facts_tsv);

-- Fix match_facts: scope to workspace and actually use FTS
CREATE OR REPLACE FUNCTION public.match_facts(
  p_workspace uuid,
  p_search text,
  p_topk integer DEFAULT 10
) RETURNS TABLE(key text, value jsonb)
LANGUAGE sql STABLE AS $$
WITH q AS (
  SELECT websearch_to_tsquery('english', p_search) AS t
)
SELECT f.key, f.value
FROM public.workspace_facts f, q
WHERE f.workspace_id = p_workspace
  AND (
    f.facts_tsv @@ q.t
    OR similarity(f.key, p_search) > 0.2
    OR similarity(f.value::text, p_search) > 0.2
  )
ORDER BY greatest(
  similarity(f.key, p_search),
  similarity(f.value::text, p_search)
) DESC
LIMIT p_topk;
$$;

-- Ensure match_chunks uses p_min_sim
CREATE OR REPLACE FUNCTION public.match_chunks(
  p_workspace uuid,
  p_query public.vector,
  p_topk integer DEFAULT 10,
  p_min_sim double precision DEFAULT 0.25
) RETURNS TABLE(id bigint, chunk_text text, similarity double precision)
LANGUAGE sql STABLE AS $$
  SELECT DISTINCT ON (left(chunk_text,120))
         id,
         chunk_text,
         1 - (embedding <=> p_query) AS similarity
  FROM   public.workspace_embeddings
  WHERE  workspace_id = p_workspace
     AND 1 - (embedding <=> p_query) >= p_min_sim
  ORDER  BY left(chunk_text,120),
            embedding <=> p_query
  LIMIT  p_topk
$$;

-- Vector indexes for fast ANN on notes and embeddings, plus cheap filters
CREATE INDEX IF NOT EXISTS we_workspace_id_idx ON public.workspace_embeddings (workspace_id);
CREATE INDEX IF NOT EXISTS wn_workspace_id_idx ON public.workspace_notes (workspace_id);

-- Skip building IVFFLAT index on workspace_notes to avoid maintenance_work_mem errors.
-- We will query note embeddings via workspace_embeddings (note_id IS NOT NULL),
-- which already has an IVFFLAT index.

-- Facts indexes to match query patterns
-- Trigram on value::text (replace jsonb_path_ops which doesn't help here)
DO $$ BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_class c JOIN pg_namespace n ON n.oid = c.relnamespace
    WHERE c.relname = 'facts_value_idx' AND n.nspname = 'public'
  ) THEN
    EXECUTE 'DROP INDEX public.facts_value_idx';
  END IF;
END $$;

CREATE INDEX IF NOT EXISTS facts_value_trgm_idx
ON public.workspace_facts USING gin ((value::text) gin_trgm_ops);

-- (moved earlier)

-- Add note_id to embeddings for unified storage of note and file vectors
ALTER TABLE public.workspace_embeddings
  ADD COLUMN IF NOT EXISTS note_id uuid;

-- Idempotency: unique logical key across (workspace, file/note, chunk)
DROP INDEX IF EXISTS we_unique_chunk_source;
DROP INDEX IF EXISTS we_unique_chunk;
CREATE UNIQUE INDEX IF NOT EXISTS we_unique_chunk
ON public.workspace_embeddings (
  workspace_id,
  file_id,
  note_id,
  chunk_index
);

-- Normalize vectors on write so cosine distance works as expected
CREATE OR REPLACE FUNCTION public.normalize_embedding() RETURNS trigger AS $$
BEGIN
  IF NEW.embedding IS NOT NULL THEN
    NEW.embedding := l2_normalize(NEW.embedding);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS normalize_embedding_trg ON public.workspace_embeddings;
CREATE TRIGGER normalize_embedding_trg
BEFORE INSERT OR UPDATE ON public.workspace_embeddings
FOR EACH ROW EXECUTE FUNCTION public.normalize_embedding();

DROP TRIGGER IF EXISTS normalize_note_embedding_trg ON public.workspace_notes;
CREATE TRIGGER normalize_note_embedding_trg
BEFORE INSERT OR UPDATE ON public.workspace_notes
FOR EACH ROW EXECUTE FUNCTION public.normalize_embedding();

-- Redefine match_notes to use workspace_embeddings (leverages ivfflat on embeddings)
CREATE OR REPLACE FUNCTION public.match_notes(
  p_workspace uuid,
  p_query public.vector,
  p_topk integer DEFAULT 6
) RETURNS TABLE(id uuid, body text, similarity double precision)
LANGUAGE sql STABLE AS $$
  SELECT n.id,
         n.body,
         1 - (e.embedding <=> p_query) AS similarity
  FROM   public.workspace_embeddings e
  JOIN   public.workspace_notes n ON n.id = e.note_id
  WHERE  e.workspace_id = p_workspace
     AND e.note_id IS NOT NULL
  ORDER  BY e.embedding <=> p_query
  LIMIT  p_topk
$$;


